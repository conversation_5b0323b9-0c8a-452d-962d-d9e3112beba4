package email

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"net/mail"
	"path/filepath"
	"strings"
	"time"

	"github.com.cn/tmc/langchaingo/llms/ollama"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/philippgille/chromem-go"
	"github.com/tmc/langchaingo/llms"
	"github.com/xuri/excelize/v2"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/conf"

	// Unidoc imports for PDF processing
	unipdfcommon "github.com/unidoc/unidoc/common"
	unipdf "github.com/unidoc/unidoc/pdf/model"
)

// 📧 Email Analysis Service - Comprehensive Email & Attachment Processing
type EmailAnalysisService struct {
	log      *log.Helper
	vectorDB *chromem.DB
	llm      llms.Model
	gemma3   *ai.Gemma3Service
	config   *EmailAnalysisConfig
}

// 📊 Email Analysis Configuration
type EmailAnalysisConfig struct {
	MaxAttachmentSize int64    `json:"max_attachment_size"`
	SupportedFormats  []string `json:"supported_formats"`
	OllamaURL         string   `json:"ollama_url"`
	VectorDBPath      string   `json:"vector_db_path"`
}

// 📨 Email Analysis Result
type EmailAnalysisResult struct {
	EmailID         string                 `json:"email_id"`
	Subject         string                 `json:"subject"`
	From            string                 `json:"from"`
	To              []string               `json:"to"`
	Timestamp       time.Time              `json:"timestamp"`
	BodyAnalysis    *TextAnalysis          `json:"body_analysis"`
	AttachmentCount int                    `json:"attachment_count"`
	Attachments     []*AttachmentAnalysis  `json:"attachments"`
	Sentiment       string                 `json:"sentiment"`
	SentimentScore  float64                `json:"sentiment_score"`
	Priority        string                 `json:"priority"`
	Category        string                 `json:"category"`
	ActionItems     []string               `json:"action_items"`
	HVACRelevance   *HVACRelevanceAnalysis `json:"hvac_relevance"`
	VectorEmbedding []float32              `json:"vector_embedding,omitempty"`
}

// 📄 Text Analysis
type TextAnalysis struct {
	Content    string   `json:"content"`
	WordCount  int      `json:"word_count"`
	Language   string   `json:"language"`
	KeyPhrases []string `json:"key_phrases"`
	Summary    string   `json:"summary"`
	Entities   []string `json:"entities"`
}

// 📎 Attachment Analysis
type AttachmentAnalysis struct {
	Filename    string        `json:"filename"`
	ContentType string        `json:"content_type"`
	Size        int64         `json:"size"`
	Format      string        `json:"format"`
	TextContent string        `json:"text_content,omitempty"`
	Analysis    *TextAnalysis `json:"analysis,omitempty"`
	ExcelData   *ExcelData    `json:"excel_data,omitempty"`
	IsProcessed bool          `json:"is_processed"`
	Error       string        `json:"error,omitempty"`
}

// 📊 Excel Data Structure
type ExcelData struct {
	Sheets   []string            `json:"sheets"`
	RowCount int                 `json:"row_count"`
	ColCount int                 `json:"col_count"`
	Headers  []string            `json:"headers"`
	Summary  string              `json:"summary"`
	Data     []map[string]string `json:"data,omitempty"`
}

// 🔧 HVAC Relevance Analysis
type HVACRelevanceAnalysis struct {
	IsHVACRelated     bool     `json:"is_hvac_related"`
	Confidence        float64  `json:"confidence"`
	HVACKeywords      []string `json:"hvac_keywords"`
	ServiceType       string   `json:"service_type"`
	Urgency           string   `json:"urgency"`
	EstimatedCost     string   `json:"estimated_cost,omitempty"`
	RecommendedAction string   `json:"recommended_action"`
}

// NewEmailAnalysisService creates a new email analysis service
func NewEmailAnalysisService(config *EmailAnalysisConfig, logger log.Logger) (*EmailAnalysisService, error) {
	log := log.NewHelper(logger)

	// Initialize Vector Database
	vectorDB := chromem.NewDB()

	// Initialize Ollama LLM
	llm, err := ollama.New(ollama.WithServerURL(config.OllamaURL))
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Ollama: %w", err)
	}

	// Initialize Gemma 3 Service with conf.AI_Model
	gemma3Config := &conf.AI_Model{
		Endpoint:  config.OllamaURL,
		ModelName: "gemma3:4b-instruct",
		MaxTokens: 8192,
	}

	gemma3Service := ai.NewGemma3Service(gemma3Config, logger)

	return &EmailAnalysisService{
		log:      log,
		vectorDB: vectorDB,
		llm:      llm,
		gemma3:   gemma3Service,
		config:   config,
	}, nil
}

// 🔍 AnalyzeEmail - Main email analysis function
func (s *EmailAnalysisService) AnalyzeEmail(ctx context.Context, emailData []byte) (*EmailAnalysisResult, error) {
	s.log.WithContext(ctx).Info("Starting comprehensive email analysis")

	// Parse email
	msg, err := mail.ReadMessage(strings.NewReader(string(emailData)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse email: %w", err)
	}

	result := &EmailAnalysisResult{
		EmailID:   generateEmailID(),
		Subject:   msg.Header.Get("Subject"),
		From:      msg.Header.Get("From"),
		To:        strings.Split(msg.Header.Get("To"), ","),
		Timestamp: time.Now(),
	}

	// Parse multipart message for attachments
	mediaType, params, err := mime.ParseMediaType(msg.Header.Get("Content-Type"))
	if err == nil && strings.HasPrefix(mediaType, "multipart/") {
		err = s.processMultipartMessage(ctx, msg.Body, params["boundary"], result)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to process multipart message: %v", err)
		}
	} else {
		// Single part message
		body, err := io.ReadAll(msg.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read email body: %w", err)
		}
		result.BodyAnalysis = s.analyzeText(ctx, string(body))
	}

	// Perform AI analysis with Gemma 3
	err = s.performAdvancedAIAnalysis(ctx, result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Advanced AI analysis failed: %v", err)
		// Fallback to basic analysis
		err = s.performAIAnalysis(ctx, result)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Basic AI analysis also failed: %v", err)
		}
	}

	// Store in vector database
	err = s.storeInVectorDB(ctx, result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to store in vector DB: %v", err)
	}

	s.log.WithContext(ctx).Infof("Email analysis completed for: %s", result.Subject)
	return result, nil
}

// 📎 Process multipart message with attachments
func (s *EmailAnalysisService) processMultipartMessage(ctx context.Context, body io.Reader, boundary string, result *EmailAnalysisResult) error {
	mr := multipart.NewReader(body, boundary)

	for {
		part, err := mr.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}

		disposition := part.Header.Get("Content-Disposition")
		if strings.Contains(disposition, "attachment") || strings.Contains(disposition, "inline") {
			// Process attachment
			attachment, err := s.processAttachment(ctx, part)
			if err != nil {
				s.log.WithContext(ctx).Warnf("Failed to process attachment: %v", err)
				continue
			}
			result.Attachments = append(result.Attachments, attachment)
			result.AttachmentCount++
		} else {
			// Process email body
			bodyContent, err := io.ReadAll(part)
			if err != nil {
				continue
			}
			if result.BodyAnalysis == nil {
				result.BodyAnalysis = s.analyzeText(ctx, string(bodyContent))
			}
		}
	}

	return nil
}

// 📎 Process individual attachment
func (s *EmailAnalysisService) processAttachment(ctx context.Context, part *multipart.Part) (*AttachmentAnalysis, error) {
	filename := part.FileName()
	if filename == "" {
		filename = "unnamed_attachment"
	}

	contentType := part.Header.Get("Content-Type")
	data, err := io.ReadAll(part)
	if err != nil {
		return nil, err
	}

	attachment := &AttachmentAnalysis{
		Filename:    filename,
		ContentType: contentType,
		Size:        int64(len(data)),
		Format:      strings.ToLower(filepath.Ext(filename)),
	}

	// Check size limit
	if attachment.Size > s.config.MaxAttachmentSize {
		attachment.Error = "File too large"
		return attachment, nil
	}

	// Process based on file type
	switch attachment.Format {
	case ".xlsx", ".xls":
		err = s.processExcelFile(ctx, data, attachment)
	case ".txt", ".csv":
		attachment.TextContent = string(data)
		attachment.Analysis = s.analyzeText(ctx, attachment.TextContent)
	case ".pdf":
		err = s.processPDFFile(ctx, data, attachment)
	default:
		attachment.Error = "Unsupported file format"
	}

	if err != nil {
		attachment.Error = err.Error()
	} else {
		attachment.IsProcessed = true
	}

	return attachment, nil
}

// 📊 Process Excel files
func (s *EmailAnalysisService) processExcelFile(ctx context.Context, data []byte, attachment *AttachmentAnalysis) error {
	// Create temporary file for excelize
	f, err := excelize.OpenReader(strings.NewReader(string(data)))
	if err != nil {
		return err
	}
	defer f.Close()

	excelData := &ExcelData{
		Sheets: f.GetSheetList(),
	}

	// Process first sheet
	if len(excelData.Sheets) > 0 {
		sheetName := excelData.Sheets[0]
		rows, err := f.GetRows(sheetName)
		if err != nil {
			return err
		}

		excelData.RowCount = len(rows)
		if len(rows) > 0 {
			excelData.ColCount = len(rows[0])
			excelData.Headers = rows[0]

			// Extract sample data (first 10 rows)
			for i, row := range rows {
				if i == 0 || i > 10 {
					continue
				}
				rowData := make(map[string]string)
				for j, cell := range row {
					if j < len(excelData.Headers) {
						rowData[excelData.Headers[j]] = cell
					}
				}
				excelData.Data = append(excelData.Data, rowData)
			}
		}

		excelData.Summary = fmt.Sprintf("Excel file with %d sheets, %d rows, %d columns",
			len(excelData.Sheets), excelData.RowCount, excelData.ColCount)
	}

	attachment.ExcelData = excelData
	return nil
}

// 📄 Process PDF files
func (s *EmailAnalysisService) processPDFFile(ctx context.Context, data []byte, attachment *AttachmentAnalysis) error {
	// Set Unidoc log level to avoid excessive logging
	unipdfcommon.SetLogger(unipdfcommon.NewConsoleLogger(unipdfcommon.LogLevelError))

	pdfReader, err := unipdf.NewPdfReader(bytes.NewReader(data))
	if err != nil {
		return fmt.Errorf("failed to read PDF: %w", err)
	}

	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("failed to get number of pages: %w", err)
	}

	var textBuilder strings.Builder
	for i := 0; i < numPages; i++ {
		pageNum := i + 1
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to get page %d from PDF: %v", pageNum, err)
			continue
		}

		text, err := page.GetText()
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to extract text from page %d: %v", pageNum, err)
			continue
		}
		textBuilder.WriteString(text)
	}

	attachment.TextContent = textBuilder.String()
	attachment.Analysis = s.analyzeText(ctx, attachment.TextContent) // Analyze extracted text
	return nil
}

// 📝 Analyze text content using Gemma 3 for richer insights
func (s *EmailAnalysisService) analyzeText(ctx context.Context, content string) *TextAnalysis {
	words := strings.Fields(content)

	// Prepare a minimal HVAC analysis request for text content
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: content,
		AnalysisType: "quick", // Request a quick analysis for text content
	}

	// Perform Gemma 3 analysis
	gemmaResponse, err := s.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		s.log.WithContext(ctx).Errorf("Failed to analyze text content with Gemma 3: %v", err)
		// Fallback to basic text analysis if AI fails
		return &TextAnalysis{
			Content:    content,
			WordCount:  len(words),
			Language:   "en", // Simple detection
			KeyPhrases: s.extractKeyPhrasesFallback(content),
			Summary:    s.generateSummaryFallback(content),
			Entities:   s.extractEntitiesFallback(content),
		}
	}

	// Map Gemma 3 response to TextAnalysis
	textAnalysis := &TextAnalysis{
		Content:   content,
		WordCount: len(words),
		Language:  "en", // Assume English for now, could be improved by Gemma
		Summary:   gemmaResponse.Summary,
	}

	if gemmaResponse.HVACRelevance != nil {
		textAnalysis.KeyPhrases = gemmaResponse.HVACRelevance.HVACKeywords
		if len(gemmaResponse.HVACRelevance.EquipmentMentioned) > 0 {
			textAnalysis.KeyPhrases = append(textAnalysis.KeyPhrases, gemmaResponse.HVACRelevance.EquipmentMentioned...)
		}
	}

	if gemmaResponse.TechnicalAnalysis != nil {
		textAnalysis.Entities = append(textAnalysis.Entities, gemmaResponse.TechnicalAnalysis.TechnicalTerms...)
	}
	if gemmaResponse.CustomerAnalysis != nil {
		textAnalysis.Entities = append(textAnalysis.Entities, gemmaResponse.CustomerAnalysis.CustomerType)
	}
	if gemmaResponse.ActionPlan != nil {
		textAnalysis.Entities = append(textAnalysis.Entities, gemmaResponse.ActionPlan.AssignedTechnician)
	}

	return textAnalysis
}

// 🤖 Perform AI analysis using Gemma 3 (replaces previous basic AI analysis)
func (s *EmailAnalysisService) performAIAnalysis(ctx context.Context, result *EmailAnalysisResult) error {
	// This function is now a fallback if performAdvancedAIAnalysis fails.
	// It should ideally be removed once advanced analysis is robust.
	s.log.WithContext(ctx).Warn("Falling back to basic AI analysis. Consider removing this fallback.")
	return nil // No actual basic analysis implemented here anymore
}

// 🗺️ Map Gemma 3 response to EmailAnalysisResult (moved from below for clarity)
func (s *EmailAnalysisService) mapGemmaResponseToResult(gemmaResp *ai.HVACAnalysisResponse, result *EmailAnalysisResult) {
	// Update HVAC relevance
	if gemmaResp.HVACRelevance != nil {
		result.HVACRelevance = &HVACRelevanceAnalysis{
			IsHVACRelated:     gemmaResp.HVACRelevance.IsHVACRelated,
			Confidence:        gemmaResp.HVACRelevance.Confidence,
			HVACKeywords:      gemmaResp.HVACRelevance.HVACKeywords,
			ServiceType:       gemmaResp.HVACRelevance.ServiceCategory,
			Urgency:           gemmaResp.HVACRelevance.UrgencyLevel,
			EstimatedCost:     gemmaResp.HVACRelevance.EstimatedCost, // Added from Gemma response
			RecommendedAction: gemmaResp.RecommendedResponse,
		}

		// Add equipment mentioned
		if len(gemmaResp.HVACRelevance.EquipmentMentioned) > 0 {
			result.HVACRelevance.HVACKeywords = append(result.HVACRelevance.HVACKeywords, gemmaResp.HVACRelevance.EquipmentMentioned...)
		}
	}

	// Update sentiment analysis
	if gemmaResp.SentimentAnalysis != nil {
		result.Sentiment = gemmaResp.SentimentAnalysis.OverallSentiment
		result.SentimentScore = gemmaResp.SentimentAnalysis.SentimentScore
	}

	// Update priority assessment
	if gemmaResp.PriorityAssessment != nil {
		result.Priority = gemmaResp.PriorityAssessment.PriorityLevel
	}

	// Update action items from action plan
	if gemmaResp.ActionPlan != nil {
		result.ActionItems = append(result.ActionItems, gemmaResp.ActionPlan.ImmediateActions...)
		result.ActionItems = append(result.ActionItems, gemmaResp.ActionPlan.ScheduledActions...)
	}

	// Update category based on HVAC relevance
	if result.HVACRelevance != nil && result.HVACRelevance.IsHVACRelated {
		result.Category = "hvac_service"
	}

	// Update TextAnalysis fields from Gemma response
	if result.BodyAnalysis != nil {
		result.BodyAnalysis.Summary = gemmaResp.Summary
		// Gemma response doesn't directly provide key phrases or entities for the whole body,
		// but we can infer from other fields or add a specific prompt for it.
		// For now, we'll use the HVACKeywords as key phrases.
		result.BodyAnalysis.KeyPhrases = result.HVACRelevance.HVACKeywords
		// Entities could be extracted from TechnicalAnalysis or CustomerAnalysis if available
		if gemmaResp.TechnicalAnalysis != nil {
			result.BodyAnalysis.Entities = append(result.BodyAnalysis.Entities, gemmaResp.TechnicalAnalysis.TechnicalTerms...)
		}
		if gemmaResp.CustomerAnalysis != nil {
			result.BodyAnalysis.Entities = append(result.BodyAnalysis.Entities, gemmaResp.CustomerAnalysis.CustomerType)
		}
	}
}

// Helper functions (fallbacks for when Gemma 3 analysis fails)
func (s *EmailAnalysisService) extractKeyPhrasesFallback(content string) []string {
	hvacKeywords := []string{"HVAC", "air conditioning", "heating", "cooling", "repair", "maintenance", "installation"}
	var found []string
	contentLower := strings.ToLower(content)
	for _, keyword := range hvacKeywords {
		if strings.Contains(contentLower, strings.ToLower(keyword)) {
			found = append(found, keyword)
		}
	}
	return found
}

func (s *EmailAnalysisService) generateSummaryFallback(content string) string {
	if len(content) > 200 {
		return content[:200] + "..."
	}
	return content
}

func (s *EmailAnalysisService) extractEntitiesFallback(content string) []string {
	return []string{}
}

func (s *EmailAnalysisService) determinePriority(ctx context.Context, result *EmailAnalysisResult) string {
	if result.HVACRelevance != nil && result.HVACRelevance.Urgency == "high" {
		return "high"
	}
	if result.Sentiment == "negative" {
		return "medium"
	}
	return "normal"
}

func (s *EmailAnalysisService) categorizeEmail(ctx context.Context, result *EmailAnalysisResult) string {
	if result.HVACRelevance != nil && result.HVACRelevance.IsHVACRelated {
		return "hvac_service"
	}
	return "general"
}

func (s *EmailAnalysisService) extractActionItems(ctx context.Context, content string) []string {
	actionWords := []string{"schedule", "call", "visit", "repair", "replace", "install"}
	contentLower := strings.ToLower(content)
	var actions []string

	for _, word := range actionWords {
		if strings.Contains(contentLower, word) {
			actions = append(actions, fmt.Sprintf("Action needed: %s", word))
		}
	}
	return actions
}

func (s *EmailAnalysisService) determineServiceType(keywords []string) string {
	for _, keyword := range keywords {
		switch keyword {
		case "repair":
			return "repair"
		case "installation", "install":
			return "installation"
		case "maintenance":
			return "maintenance"
		}
	}
	return "general"
}

func (s *EmailAnalysisService) recommendAction(isRelevant bool, urgency string) string {
	if !isRelevant {
		return "No HVAC action required"
	}

	if urgency == "high" {
		return "Schedule emergency service call immediately"
	}

	return "Schedule service appointment within 24-48 hours"
}

// 🗄️ Store in vector database
func (s *EmailAnalysisService) storeInVectorDB(ctx context.Context, result *EmailAnalysisResult) error {
	if result.BodyAnalysis == nil {
		return nil
	}

	// Create document for vector storage
	doc := chromem.Document{
		ID:      result.EmailID,
		Content: result.BodyAnalysis.Content,
		Metadata: map[string]string{
			"subject":   result.Subject,
			"from":      result.From,
			"timestamp": result.Timestamp.Format("2006-01-02T15:04:05Z"),
			"sentiment": result.Sentiment,
			"category":  result.Category,
			"priority":  result.Priority,
		},
	}

	// Add to vector database
	collection, err := s.vectorDB.GetOrCreateCollection("emails", nil, nil)
	if err != nil {
		return fmt.Errorf("failed to get collection: %w", err)
	}

	err = collection.Add(ctx, []string{doc.ID}, nil, []map[string]string{doc.Metadata}, []string{doc.Content})
	if err != nil {
		return fmt.Errorf("failed to add document to vector DB: %w", err)
	}

	return nil
}

// 🔍 Search similar emails
func (s *EmailAnalysisService) SearchSimilarEmails(ctx context.Context, query string, limit int) ([]chromem.Result, error) {
	collection, err := s.vectorDB.GetOrCreateCollection("emails", nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get collection: %w", err)
	}

	results, err := collection.Query(ctx, query, limit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to search vector DB: %w", err)
	}

	return results, nil
}

// 🤖 Perform advanced AI analysis using Gemma 3
func (s *EmailAnalysisService) performAdvancedAIAnalysis(ctx context.Context, result *EmailAnalysisResult) error {
	if result.BodyAnalysis == nil {
		return fmt.Errorf("no body analysis available")
	}

	s.log.WithContext(ctx).Info("Starting advanced Gemma 3 analysis")

	// Prepare HVAC analysis request
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: result.BodyAnalysis.Content,
		Subject:      result.Subject,
		From:         result.From,
		To:           result.To,
		AnalysisType: "comprehensive",
	}

	// Add attachments data
	for _, attachment := range result.Attachments {
		if attachment.IsProcessed && attachment.TextContent != "" {
			hvacReq.Attachments = append(hvacReq.Attachments, &ai.AttachmentData{
				Filename:    attachment.Filename,
				ContentType: attachment.ContentType,
				Content:     attachment.TextContent,
				Size:        attachment.Size,
			})
		}
	}

	// Add HVAC context
	hvacReq.HVACContext = &ai.HVACContextData{
		ServiceType:     "general",
		SeasonalContext: getCurrentSeason(),
		BusinessHours:   "8:00-17:00 Mon-Fri, 9:00-15:00 Sat",
	}

	// Perform Gemma 3 analysis
	gemmaResponse, err := s.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		return fmt.Errorf("Gemma 3 analysis failed: %w", err)
	}

	// Map Gemma 3 response to EmailAnalysisResult
	s.mapGemmaResponseToResult(gemmaResponse, result)

	s.log.WithContext(ctx).Infof("Advanced Gemma 3 analysis completed in %v", gemmaResponse.ProcessingTime)
	return nil
}

// 🌍 Get current season for context
func getCurrentSeason() string {
	month := time.Now().Month()
	switch {
	case month >= 3 && month >= 5:
		return "spring"
	case month >= 6 && month <= 8:
		return "summer"
	case month >= 9 && month <= 11:
		return "autumn"
	default:
		return "winter"
	}
}

// 🆔 Generate unique email ID
func generateEmailID() string {
	return fmt.Sprintf("email_%d", time.Now().UnixNano())
}

</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.



New problems detected after saving the file:
GoBackend-Kratos-fresh/internal/email/analysis.go
- [compiler Error] Line 15: could not import github.com.cn/tmc/langchaingo/llms/ollama (no required module provides package "github.com.cn/tmc/langchaingo/llms/ollama")
- [compiler Error] Line 25: could not import github.com/unidoc/unidoc/common (no required module provides package "github.com/unidoc/unidoc/common")
- [compiler Error] Line 26: could not import github.com/unidoc/unidoc/pdf/model (no required module provides package "github.com/unidoc/unidoc/pdf/model")<environment_details>
# VSCode Visible Files
GoBackend-Kratos-fresh/internal/email/analysis.go

# VSCode Open Tabs
hvac-remix/app/components/ui/slider.tsx
hvac-remix/app/components/opportunities/PipelineBoard.tsx
hvac-remix/app/routes/financial.dashboard.tsx
scripts/setup_local.sh
hvac-solidjs-cosmic/src/App.tsx
hvac-solidjs-cosmic/tailwind.config.js
hvac-solidjs-cosmic/docs/guidelines/COSMIC_GUIDELINES.md
hvac-solidjs-cosmic/docs/guidelines/FINAL_VISION_SOLIDJS.md
hvac-solidjs-cosmic/docs/guidelines/FINAL_VISION_GO_BACKEND.md
hvac-solidjs-cosmic/tsconfig.app.json
hvac-solidjs-cosmic/src/lib/api/types.ts
hvac-solidjs-cosmic/src/lib/api/client.ts
hvac-solidjs-cosmic/src/lib/api/hooks.ts
hvac-solidjs-cosmic/src/lib/motion/cosmicSprings.ts
hvac-solidjs-cosmic/src/components/organisms/CosmicDashboardEnhanced.tsx
GoBackend-Kratos-fresh/internal/biz/errors.go
GoBackend-Kratos-fresh/internal/server/validation.go
GoBackend-Kratos-fresh/internal/server/enhanced_trpc_example.go
GoBackend-Kratos-fresh/internal/server/circuitbreaker.go
GoBackend-Kratos-fresh/internal/ai/gemma3_service.go
GoBackend-Kratos-fresh/internal/email/analysis.go
DON.md
DIVINE_QUALITY_ENHANCEMENT_PLAN.md
../../.vscode-server/data/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json
GoBackend-Kratos/api/hvac/v1/hvac.proto
GoBackend-Kratos/migrations/012_add_leads_and_campaigns_tables.sql
GoBackend-Kratos/internal/biz/errors.go
GoBackend-Kratos/internal/entity/lead.go
GoBackend-Kratos/internal/entity/campaign.go
GoBackend-Kratos/internal/entity/lead_campaign_interaction.go
GoBackend-Kratos/internal/biz/lead.go
GoBackend-Kratos/internal/biz/campaign.go
GoBackend-Kratos/internal/data/models.go
GoBackend-Kratos/internal/data/lead.go
GoBackend-Kratos/internal/data/campaign.go
GoBackend-Kratos/internal/data/lead_campaign_interaction.go
GoBackend-Kratos/internal/data/data.go
GoBackend-Kratos/internal/biz/lead_campaign_interaction.go
GoBackend-Kratos/internal/biz/biz.go
GoBackend-Kratos/go.mod
GoBackend-Kratos/internal/storage/minio_service.go
GoBackend-Kratos/internal/extraction/xlsx_extractor.go
GoBackend-Kratos/internal/extraction/pdf_extractor.go
GoBackend-Kratos/cmd/dual-email-processor/main.go
GoBackend-Kratos-fresh/configs/email-intelligence.yaml
GoBackend-Kratos-fresh/cmd/dual-email-processor/main.go
utos/GoBackend-Kratos/internal/mcp/tools/analytics.go
utos/GoBackend-Kratos/internal/errors/hvac_errors.go
hvac-remix/app/components/ui/button.tsx
hvac-remix/vite.config.ts
GoBackend-Kratos/internal/errors/hvac_errors.go
GoBackend-Kratos/internal/mcp/tools/analytics.go

# Current Time
5/28/2025, 11:05:29 PM (Europe/Warsaw, UTC+2:00)

# Context Window Usage
536,755 / 1,048.576K tokens used (51%)

# Current Mode
ACT MODE
</environment_details>
