//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package basic ;import _c "github.com/unidoc/unipdf/v3/internal/jbig2/errors";func (_fc *IntSlice )Add (v int )error {if _fc ==nil {return _c .Error ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0041\u0064\u0064","\u0073\u006c\u0069\u0063\u0065\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};*_fc =append (*_fc ,v );return nil ;};func (_dcb IntSlice )Get (index int )(int ,error ){if index > len (_dcb )-1{return 0,_c .Errorf ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",index );
};return _dcb [index ],nil ;};type NumSlice []float32 ;func (_fgf *Stack )Len ()int {return len (_fgf .Data )};func NewIntSlice (i int )*IntSlice {_fg :=IntSlice (make ([]int ,i ));return &_fg };func Max (x ,y int )int {if x > y {return x ;};return y ;
};func (_fgd *Stack )Pop ()(_cdge interface{},_bc bool ){_cdge ,_bc =_fgd .peek ();if !_bc {return nil ,_bc ;};_fgd .Data =_fgd .Data [:_fgd .top ()];return _cdge ,true ;};func (_bb *Stack )Peek ()(_faa interface{},_gba bool ){return _bb .peek ()};type IntSlice []int ;
type Stack struct{Data []interface{};Aux *Stack ;};func (_dc IntsMap )GetSlice (key uint64 )([]int ,bool ){_f ,_de :=_dc [key ];if !_de {return nil ,false ;};return _f ,true ;};func (_gb IntsMap )Get (key uint64 )(int ,bool ){_cb ,_e :=_gb [key ];if !_e {return 0,false ;
};if len (_cb )==0{return 0,false ;};return _cb [0],true ;};func (_fb *NumSlice )Add (v float32 ){*_fb =append (*_fb ,v )};func (_a *IntSlice )Copy ()*IntSlice {_cd :=IntSlice (make ([]int ,len (*_a )));copy (_cd ,*_a );return &_cd ;};func (_eb *NumSlice )AddInt (v int ){*_eb =append (*_eb ,float32 (v ))};
func (_fa NumSlice )GetInt (i int )(int ,error ){const _ce ="\u0047\u0065\u0074\u0049\u006e\u0074";if i < 0||i > len (_fa )-1{return 0,_c .Errorf (_ce ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};_b :=_fa [i ];return int (_b +Sign (_b )*0.5),nil ;};type IntsMap map[uint64 ][]int ;func (_ba *Stack )top ()int {return len (_ba .Data )-1};func (_ff IntsMap )Delete (key uint64 ){delete (_ff ,key )};func (_cdg NumSlice )Get (i int )(float32 ,error ){if i < 0||i > len (_cdg )-1{return 0,_c .Errorf ("\u004e\u0075\u006dS\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _cdg [i ],nil ;};func (_g IntsMap )Add (key uint64 ,value int ){_g [key ]=append (_g [key ],value )};func Min (x ,y int )int {if x < y {return x ;};return y ;};func NewNumSlice (i int )*NumSlice {_af :=NumSlice (make ([]float32 ,i ));return &_af };
func Ceil (numerator ,denominator int )int {if numerator %denominator ==0{return numerator /denominator ;};return (numerator /denominator )+1;};func (_cc *Stack )Push (v interface{}){_cc .Data =append (_cc .Data ,v )};func (_ef NumSlice )GetIntSlice ()[]int {_fca :=make ([]int ,len (_ef ));
for _cg ,_cdb :=range _ef {_fca [_cg ]=int (_cdb );};return _fca ;};func (_df *Stack )peek ()(interface{},bool ){_fe :=_df .top ();if _fe ==-1{return nil ,false ;};return _df .Data [_fe ],true ;};func Sign (v float32 )float32 {if v >=0.0{return 1.0;};return -1.0;
};func Abs (v int )int {if v > 0{return v ;};return -v ;};func (_cbf IntSlice )Size ()int {return len (_cbf )};