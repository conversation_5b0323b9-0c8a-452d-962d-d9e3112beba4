# 📧 Email Intelligence System Configuration
# GoBackend-Kratos HVAC Email Analysis & Retrieval System

# 🔍 Analysis Service Configuration
analysis:
  max_attachment_size: 52428800  # 50MB in bytes
  supported_formats:
    - ".xlsx"
    - ".xls"
    - ".txt"
    - ".csv"
    - ".pdf"
    - ".docx"
  ollama_url: "http://*************:1234"
  vector_db_path: "./data/vector_db"

# 🤖 Gemma 3 AI Configuration
gemma3:
  ollama_url: "http://*************:1234"
  model_name: "gemma3:4b-instruct"
  max_tokens: 8192
  temperature: 0.7
  top_p: 0.9
  context_window: 128000
  timeout: "60s"
  retry_attempts: 3
  enable_vision: true
  image_resolution: 896

# 📮 Mailbox Configurations
mailboxes:
  # Gmail Configuration Template
  - name: "HVAC_Gmail_Primary"
    host: "imap.gmail.com"
    port: 993
    username: ""  # Set via environment variable GMAIL_USERNAME
    password: ""  # Set via environment variable GMAIL_PASSWORD
    use_ssl: true
    folder: "INBOX"
    poll_interval: 5  # minutes
    enabled: true     # Enable after configuring credentials

  # Outlook Configuration Template
  - name: "HVAC_Outlook_Support"
    host: "outlook.office365.com"
    port: 993
    username: ""  # Set via environment variable OUTLOOK_USERNAME
    password: ""  # Set via environment variable OUTLOOK_PASSWORD
    use_ssl: true
    folder: "INBOX"
    poll_interval: 10  # minutes
    enabled: false     # Enable after configuring credentials

  # Custom HVAC Business Email
  - name: "HVAC_Business_Main"
    host: ""          # Set your business email IMAP host
    port: 993
    username: ""      # Set via environment variable BUSINESS_USERNAME
    password: ""      # Set via environment variable BUSINESS_PASSWORD
    use_ssl: true
    folder: "INBOX"
    poll_interval: 3   # minutes - more frequent for business emails
    enabled: false     # Enable after configuring credentials

# 🌐 HTTP Server Configuration
http_port: 8082
enable_cors: true
log_level: "info"

# 🤖 AI Configuration
ollama_url: "http://*************:1234"
vector_db_path: "./data/email_vectors"

# ⚡ Processing Configuration
max_concurrent_analysis: 5
processing_timeout: "30s"
retry_attempts: 3

# 📊 Dashboard Configuration
dashboard:
  refresh_interval: "30s"
  max_recent_emails: 50
  enable_real_time: true

# 🔐 Security Configuration
security:
  enable_auth: false  # Set to true for production
  jwt_secret: ""      # Set via environment variable JWT_SECRET
  session_timeout: "24h"

# 📁 Storage Configuration
storage:
  data_directory: "./data"
  backup_enabled: true
  backup_interval: "24h"
  max_backup_files: 7

# 🔔 Notification Configuration
notifications:
  enabled: true
  high_priority_threshold: 0.8
  webhook_url: ""     # Set via environment variable WEBHOOK_URL
  email_alerts: false

# 📈 Monitoring Configuration
monitoring:
  metrics_enabled: true
  metrics_port: 9090
  health_check_interval: "10s"

# 🧪 Development Configuration
development:
  debug_mode: false
  mock_emails: false
  test_data_path: "./test/data"

# 🔧 Advanced Configuration
advanced:
  # Email Processing
  email_batch_size: 10
  max_email_size: 104857600  # 100MB
  attachment_extraction: true

  # Vector Database
  vector_dimensions: 384
  similarity_threshold: 0.7
  max_vector_results: 20

  # AI Processing
  sentiment_model: "default"
  classification_model: "default"
  entity_extraction: true

  # Performance
  worker_pool_size: 10
  queue_buffer_size: 1000
  gc_interval: "1h"

# 🏷️ HVAC-Specific Configuration
hvac:
  # Service Categories
  service_categories:
    - "repair"
    - "maintenance"
    - "installation"
    - "emergency"
    - "consultation"

  # Priority Keywords
  priority_keywords:
    high:
      - "emergency"
      - "urgent"
      - "broken"
      - "not working"
      - "no heat"
      - "no cooling"
    medium:
      - "repair"
      - "service"
      - "maintenance"
      - "check"
    low:
      - "quote"
      - "estimate"
      - "information"

  # Business Hours
  business_hours:
    monday: "08:00-17:00"
    tuesday: "08:00-17:00"
    wednesday: "08:00-17:00"
    thursday: "08:00-17:00"
    friday: "08:00-17:00"
    saturday: "09:00-15:00"
    sunday: "closed"

  # Emergency Response
  emergency_response:
    enabled: true
    response_time: "2h"
    after_hours_contact: ""  # Set emergency contact

# 🌍 Environment Variables Template
# Copy these to your .env file and set appropriate values:
#
# # Gmail Configuration
# GMAIL_USERNAME=<EMAIL>
# GMAIL_PASSWORD=your-app-specific-password
#
# # Outlook Configuration
# OUTLOOK_USERNAME=<EMAIL>
# OUTLOOK_PASSWORD=your-outlook-password
#
# # Business Email Configuration
# BUSINESS_USERNAME=<EMAIL>
# BUSINESS_PASSWORD=your-business-email-password
#
# # Security
# JWT_SECRET=your-super-secret-jwt-key-here
#
# # Notifications
# WEBHOOK_URL=https://your-webhook-endpoint.com/notifications
#
# # Database
# DATABASE_URL=postgres://hvac_user:hvac_password@localhost:5432/hvac_db
#
# # AI Services
# OLLAMA_URL=http://localhost:11434
#
# # Monitoring
# SENTRY_DSN=your-sentry-dsn-for-error-tracking

# 📝 Usage Instructions:
#
# 1. Copy this file to your configs directory
# 2. Create a .env file with the environment variables above
# 3. Configure your email accounts (Gmail, Outlook, Business)
# 4. Set enabled: true for the mailboxes you want to monitor
# 5. Start the service with: ./server -conf configs/email-intelligence.yaml
# 6. Access the dashboard at: http://localhost:8082/api/v1/email-analysis/dashboard/stats
#
# 🔧 Quick Start Commands:
#
# # Test mailbox connection
# curl -X POST http://localhost:8082/api/v1/retrieval/mailboxes/HVAC_Gmail_Primary/test
#
# # Start email retrieval
# curl -X POST http://localhost:8082/api/v1/retrieval/start
#
# # View dashboard stats
# curl http://localhost:8082/api/v1/email-analysis/dashboard/stats
#
# # Search emails
# curl -X POST http://localhost:8082/api/v1/email-analysis/search \
#   -H "Content-Type: application/json" \
#   -d '{"query":"HVAC repair","limit":10}'

# 🎯 Integration with GoBackend-Kratos:
#
# This email intelligence system integrates seamlessly with the existing
# GoBackend-Kratos HVAC CRM system, providing:
#
# ✅ Automatic email ingestion from multiple mailboxes
# ✅ AI-powered email analysis and categorization
# ✅ HVAC-specific sentiment and priority detection
# ✅ Attachment processing (Excel, Word, PDF)
# ✅ Vector database for semantic email search
# ✅ Real-time dashboard for email analytics
# ✅ RESTful API for integration with existing CRM
# ✅ Comprehensive logging and monitoring
#
# 🚀 Next Steps:
# - Configure your email accounts
# - Enable the mailboxes you want to monitor
# - Start the service and begin analyzing emails
# - Integrate with your existing HVAC CRM workflows
