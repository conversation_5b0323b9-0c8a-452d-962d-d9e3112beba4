// 🌌 COSMIC MOTION SCHEME - Material Design 3 Expressive with Golden Ratio
// Physics-based spring animations aligned with cosmic design principles

import { createSignal, createEffect } from 'solid-js'

// 🔢 Cosmic Constants (Golden Ratio Based)
export const COSMIC_CONSTANTS = {
  PHI: 1.618033988749,           // Golden Ratio
  FINE_STRUCTURE: 137,           // Fine Structure Constant
  COSMIC_SPEED: 299792458,       // Speed of Light (for ultra-fast animations)
  PLANCK_TIME: 5.39e-44         // Smallest meaningful time unit
} as const

// 🎯 Material Design 3 Expressive Motion Tokens
export const M3_MOTION_TOKENS = {
  // Duration tokens (in milliseconds)
  duration: {
    short1: 50,
    short2: 100,
    short3: 150,
    short4: 200,
    medium1: 250,
    medium2: 300,
    medium3: 350,
    medium4: 400,
    long1: 450,
    long2: 500,
    long3: 550,
    long4: 600,
    extraLong1: 700,
    extraLong2: 800,
    extraLong3: 900,
    extraLong4: 1000
  },
  
  // Easing curves
  easing: {
    // Standard curves
    standard: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
    standardAccelerate: 'cubic-bezier(0.3, 0.0, 1, 1)',
    standardDecelerate: 'cubic-bezier(0.0, 0.0, 0, 1)',
    
    // Emphasized curves (for expressive motion)
    emphasized: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
    emphasizedAccelerate: 'cubic-bezier(0.3, 0.0, 0.8, 0.15)',
    emphasizedDecelerate: 'cubic-bezier(0.05, 0.7, 0.1, 1.0)',
    
    // Legacy support
    legacy: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
    legacyAccelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
    legacyDecelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)'
  }
} as const

// 🌟 Cosmic Spring Configuration
export interface CosmicSpringConfig {
  tension: number      // Spring stiffness (higher = faster)
  friction: number     // Damping (higher = less bounce)
  mass?: number        // Object mass (affects inertia)
  velocity?: number    // Initial velocity
  precision?: number   // Animation precision
  clamp?: boolean      // Prevent overshoot
}

// 🎨 Cosmic Motion Schemes
export const COSMIC_MOTION_SCHEMES = {
  // 🎆 EXPRESSIVE SCHEME - For hero moments and key interactions
  expressive: {
    // Spatial animations (position, scale, rotation) - WITH overshoot
    spatial: {
      // Fast interactions (buttons, switches, small components)
      fast: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * COSMIC_CONSTANTS.PHI * 2, // ~443
        friction: Math.round(26 * COSMIC_CONSTANTS.PHI), // ~42
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig,
      
      // Default interactions (cards, modals, medium components)
      default: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * COSMIC_CONSTANTS.PHI, // ~221
        friction: 26, // Golden ratio derived
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig,
      
      // Slow interactions (page transitions, large components)
      slow: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE, // 137
        friction: Math.round(26 / COSMIC_CONSTANTS.PHI), // ~16
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig
    },
    
    // Effects animations (opacity, color, filters) - NO overshoot
    effects: {
      fast: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 3, // ~411
        friction: 40, // High friction = no bounce
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig,
      
      default: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 2, // ~274
        friction: 35,
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig,
      
      slow: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE, // 137
        friction: 30,
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig
    }
  },
  
  // ⚡ STANDARD SCHEME - For utilitarian interactions
  standard: {
    spatial: {
      fast: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 2,
        friction: 35, // Higher friction = less bounce
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig,
      
      default: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 1.5,
        friction: 30,
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig,
      
      slow: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE,
        friction: 25,
        mass: 1,
        precision: 0.01,
        clamp: false
      } as CosmicSpringConfig
    },
    
    effects: {
      fast: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 2,
        friction: 40,
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig,
      
      default: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE * 1.5,
        friction: 35,
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig,
      
      slow: {
        tension: COSMIC_CONSTANTS.FINE_STRUCTURE,
        friction: 30,
        mass: 1,
        precision: 0.01,
        clamp: true
      } as CosmicSpringConfig
    }
  }
} as const

// 🎭 Motion Context Types
export type MotionScheme = 'expressive' | 'standard'
export type MotionCategory = 'spatial' | 'effects'
export type MotionSpeed = 'fast' | 'default' | 'slow'

// 🌌 Cosmic Motion Context
export interface CosmicMotionContext {
  scheme: MotionScheme
  respectsReducedMotion: boolean
  globalSpeedMultiplier: number
}

// 🎯 Create Cosmic Motion Context
export const createCosmicMotionContext = (initialScheme: MotionScheme = 'expressive') => {
  const [scheme, setScheme] = createSignal<MotionScheme>(initialScheme)
  const [respectsReducedMotion, setRespectsReducedMotion] = createSignal(false)
  const [globalSpeedMultiplier, setGlobalSpeedMultiplier] = createSignal(1)
  
  // Check for reduced motion preference
  createEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setRespectsReducedMotion(mediaQuery.matches)
    
    const handleChange = (e: MediaQueryListEvent) => {
      setRespectsReducedMotion(e.matches)
    }
    
    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  })
  
  return {
    scheme,
    setScheme,
    respectsReducedMotion,
    setRespectsReducedMotion,
    globalSpeedMultiplier,
    setGlobalSpeedMultiplier
  }
}

// 🎨 Get Cosmic Spring Config
export const getCosmicSpringConfig = (
  category: MotionCategory,
  speed: MotionSpeed = 'default',
  scheme: MotionScheme = 'expressive',
  respectsReducedMotion: boolean = false
): CosmicSpringConfig => {
  if (respectsReducedMotion) {
    // Return immediate, non-animated config for reduced motion
    return {
      tension: 1000,
      friction: 100,
      mass: 1,
      precision: 0.01,
      clamp: true
    }
  }
  
  return COSMIC_MOTION_SCHEMES[scheme][category][speed]
}

// 🌟 Cosmic Animation Presets
export const COSMIC_PRESETS = {
  // Button interactions
  buttonHover: getCosmicSpringConfig('spatial', 'fast', 'expressive'),
  buttonPress: getCosmicSpringConfig('spatial', 'fast', 'expressive'),
  
  // Card animations
  cardEntrance: getCosmicSpringConfig('spatial', 'default', 'expressive'),
  cardHover: getCosmicSpringConfig('spatial', 'fast', 'expressive'),
  
  // Modal animations
  modalBackdrop: getCosmicSpringConfig('effects', 'default', 'expressive'),
  modalContent: getCosmicSpringConfig('spatial', 'default', 'expressive'),
  
  // Page transitions
  pageTransition: getCosmicSpringConfig('spatial', 'slow', 'expressive'),
  
  // Micro-interactions
  iconSpin: getCosmicSpringConfig('spatial', 'fast', 'standard'),
  tooltipShow: getCosmicSpringConfig('effects', 'fast', 'standard'),
  
  // Loading states
  loadingPulse: getCosmicSpringConfig('effects', 'slow', 'standard'),
  loadingBounce: getCosmicSpringConfig('spatial', 'default', 'expressive')
} as const

export default COSMIC_MOTION_SCHEMES
