// 🌌 COSMIC TRANSITIONS - Advanced Page Transition System
// Physics-based page transitions with cosmic choreography

import { createSignal, createEffect, onCleanup, Accessor } from 'solid-js'
import { createCosmicMultiSpring, createStaggeredSprings } from './cosmicSprings'
import { getCosmicSpringConfig, COSMIC_CONSTANTS } from './cosmicMotionScheme'

// 🎯 Transition Types
export type TransitionType = 
  | 'slide' | 'fade' | 'scale' | 'rotate' | 'flip' | 'cosmic' | 'divine' | 'golden'

export type TransitionDirection = 'left' | 'right' | 'up' | 'down' | 'center'

// 🌟 Transition State
export interface TransitionState {
  isTransitioning: boolean
  progress: number
  direction: TransitionDirection
  type: TransitionType
  duration: number
}

// 🎨 Transition Configuration
export interface TransitionConfig {
  type: TransitionType
  direction?: TransitionDirection
  duration?: number
  stagger?: number
  springConfig?: any
  onStart?: () => void
  onComplete?: () => void
}

// 🚀 Create Cosmic Page Transition
export const createCosmicPageTransition = (config: TransitionConfig) => {
  const [transitionState, setTransitionState] = createSignal<TransitionState>({
    isTransitioning: false,
    progress: 0,
    direction: config.direction || 'right',
    type: config.type,
    duration: config.duration || 800
  })

  // 🌈 Multi-spring for complex transitions
  const pageSpring = createCosmicMultiSpring(
    {
      x: 0,
      y: 0,
      scale: 1,
      opacity: 1,
      rotateX: 0,
      rotateY: 0,
      rotateZ: 0,
      skewX: 0,
      skewY: 0
    },
    config.springConfig || getCosmicSpringConfig('spatial', 'default', 'expressive')
  )

  // 🎭 Transition Presets
  const getTransitionValues = (type: TransitionType, direction: TransitionDirection, isEntering: boolean) => {
    const multiplier = isEntering ? 1 : -1
    
    switch (type) {
      case 'slide':
        switch (direction) {
          case 'left': return { x: -100 * multiplier, opacity: isEntering ? 1 : 0 }
          case 'right': return { x: 100 * multiplier, opacity: isEntering ? 1 : 0 }
          case 'up': return { y: -100 * multiplier, opacity: isEntering ? 1 : 0 }
          case 'down': return { y: 100 * multiplier, opacity: isEntering ? 1 : 0 }
          default: return { x: 100 * multiplier, opacity: isEntering ? 1 : 0 }
        }
      
      case 'fade':
        return { opacity: isEntering ? 1 : 0 }
      
      case 'scale':
        return { 
          scale: isEntering ? 1 : 0.8, 
          opacity: isEntering ? 1 : 0 
        }
      
      case 'rotate':
        return { 
          rotateY: isEntering ? 0 : 90 * multiplier,
          opacity: isEntering ? 1 : 0 
        }
      
      case 'flip':
        return { 
          rotateX: isEntering ? 0 : 180,
          scale: isEntering ? 1 : 0.8,
          opacity: isEntering ? 1 : 0 
        }
      
      case 'cosmic':
        return {
          x: isEntering ? 0 : 50 * multiplier,
          y: isEntering ? 0 : -30 * multiplier,
          scale: isEntering ? 1 : 0.9,
          rotateZ: isEntering ? 0 : 15 * multiplier,
          opacity: isEntering ? 1 : 0
        }
      
      case 'divine':
        return {
          scale: isEntering ? 1 : 1.1,
          rotateX: isEntering ? 0 : -10,
          rotateY: isEntering ? 0 : 20 * multiplier,
          opacity: isEntering ? 1 : 0,
          skewX: isEntering ? 0 : 5 * multiplier
        }
      
      case 'golden':
        const phi = COSMIC_CONSTANTS.PHI
        return {
          x: isEntering ? 0 : 100 / phi * multiplier,
          y: isEntering ? 0 : -100 / (phi * phi),
          scale: isEntering ? 1 : 1 / phi,
          rotateZ: isEntering ? 0 : 137 / 10, // Fine structure constant
          opacity: isEntering ? 1 : 0
        }
      
      default:
        return { opacity: isEntering ? 1 : 0 }
    }
  }

  // 🎯 Execute Transition
  const executeTransition = (isEntering: boolean = true) => {
    setTransitionState(prev => ({ ...prev, isTransitioning: true, progress: 0 }))
    config.onStart?.()

    const targetValues = getTransitionValues(
      transitionState().type,
      transitionState().direction,
      isEntering
    )

    // Set initial state for entering
    if (isEntering) {
      const initialValues = getTransitionValues(
        transitionState().type,
        transitionState().direction,
        false
      )
      pageSpring.set(initialValues)
      
      // Animate to final state
      setTimeout(() => {
        pageSpring.set(targetValues)
      }, 50)
    } else {
      pageSpring.set(targetValues)
    }

    // Complete transition after duration
    setTimeout(() => {
      setTransitionState(prev => ({ ...prev, isTransitioning: false, progress: 1 }))
      config.onComplete?.()
    }, transitionState().duration)
  }

  return {
    transitionState,
    pageSpring,
    executeTransition,
    enter: () => executeTransition(true),
    exit: () => executeTransition(false),
    isTransitioning: () => transitionState().isTransitioning
  }
}

// 🌊 Route Transition Manager
export const createCosmicRouteTransition = () => {
  const [currentRoute, setCurrentRoute] = createSignal<string>('')
  const [nextRoute, setNextRoute] = createSignal<string>('')
  const [isTransitioning, setIsTransitioning] = createSignal(false)

  const transition = createCosmicPageTransition({
    type: 'cosmic',
    direction: 'right',
    duration: 600,
    onStart: () => setIsTransitioning(true),
    onComplete: () => {
      setCurrentRoute(nextRoute())
      setIsTransitioning(false)
    }
  })

  const navigateTo = (route: string, transitionType: TransitionType = 'cosmic') => {
    if (isTransitioning()) return

    setNextRoute(route)
    
    // Update transition type
    transition.transitionState().type = transitionType
    
    // Execute exit transition
    transition.exit()
    
    // After exit, execute enter transition
    setTimeout(() => {
      transition.enter()
    }, 300)
  }

  return {
    currentRoute,
    nextRoute,
    isTransitioning,
    navigateTo,
    transition
  }
}

// 🎭 Staggered Element Transitions
export const createCosmicStaggeredTransition = (
  elementCount: number,
  config: TransitionConfig
) => {
  const staggerDelay = config.stagger || COSMIC_CONSTANTS.FINE_STRUCTURE // 137ms
  
  const springs = createStaggeredSprings(
    elementCount,
    0,
    config.springConfig || getCosmicSpringConfig('spatial', 'default', 'expressive'),
    staggerDelay
  )

  const animateIn = () => {
    springs.setStaggered(1)
  }

  const animateOut = () => {
    springs.setStaggered(0)
  }

  return {
    springs: springs.springs,
    animateIn,
    animateOut,
    setAll: springs.setAll,
    stopAll: springs.stopAll
  }
}

// 🌟 Cosmic Transition Presets
export const COSMIC_TRANSITION_PRESETS = {
  // Dashboard to page transitions
  dashboardToCustomers: {
    type: 'slide' as TransitionType,
    direction: 'left' as TransitionDirection,
    duration: 500,
    springConfig: getCosmicSpringConfig('spatial', 'default', 'expressive')
  },
  
  // Modal transitions
  modalEntrance: {
    type: 'scale' as TransitionType,
    direction: 'center' as TransitionDirection,
    duration: 400,
    springConfig: getCosmicSpringConfig('spatial', 'fast', 'expressive')
  },
  
  // Card detail transitions
  cardToDetail: {
    type: 'cosmic' as TransitionType,
    direction: 'right' as TransitionDirection,
    duration: 600,
    springConfig: getCosmicSpringConfig('spatial', 'default', 'expressive')
  },
  
  // Settings page transitions
  settingsSlide: {
    type: 'divine' as TransitionType,
    direction: 'up' as TransitionDirection,
    duration: 700,
    springConfig: getCosmicSpringConfig('spatial', 'slow', 'expressive')
  },
  
  // Golden ratio transition
  goldenTransition: {
    type: 'golden' as TransitionType,
    direction: 'right' as TransitionDirection,
    duration: Math.round(COSMIC_CONSTANTS.PHI * 500), // ~809ms
    springConfig: getCosmicSpringConfig('spatial', 'default', 'expressive')
  }
} as const

// 🎨 Transition Utilities
export const interpolateTransition = (
  progress: number,
  from: number,
  to: number,
  easing: 'linear' | 'ease' | 'cosmic' = 'cosmic'
): number => {
  if (easing === 'cosmic') {
    // Golden ratio easing
    const phi = COSMIC_CONSTANTS.PHI
    progress = Math.pow(progress, 1 / phi)
  } else if (easing === 'ease') {
    progress = progress * progress * (3 - 2 * progress)
  }
  
  return from + (to - from) * progress
}

export const getTransitionCSS = (spring: any) => {
  const values = spring.get()
  return {
    transform: `
      translateX(${values.x || 0}px) 
      translateY(${values.y || 0}px) 
      scale(${values.scale || 1}) 
      rotateX(${values.rotateX || 0}deg) 
      rotateY(${values.rotateY || 0}deg) 
      rotateZ(${values.rotateZ || 0}deg)
      skewX(${values.skewX || 0}deg)
      skewY(${values.skewY || 0}deg)
    `.trim(),
    opacity: values.opacity || 1
  }
}

export default {
  createCosmicPageTransition,
  createCosmicRouteTransition,
  createCosmicStaggeredTransition,
  COSMIC_TRANSITION_PRESETS,
  interpolateTransition,
  getTransitionCSS
}
