// 🌌 COSMIC MICRO-INTERACTIONS - Advanced Micro-Interaction Library
// Physics-based micro-interactions with cosmic precision

import { createSignal, createEffect, onCleanup, Accessor } from 'solid-js'
import { createCosmicMultiSpring, createCosmicSpring } from './cosmicSprings'
import { getCosmicSpringConfig, COSMIC_CONSTANTS } from './cosmicMotionScheme'

// 🎯 Micro-Interaction Types
export type MicroInteractionType = 
  | 'hover' | 'click' | 'focus' | 'success' | 'error' | 'warning' | 'info' | 'cosmic' | 'divine'

// 🌟 Interaction State
export interface InteractionState {
  isActive: boolean
  type: MicroInteractionType
  intensity: number
  duration: number
  timestamp: number
}

// 🎨 Micro-Interaction Configuration
export interface MicroInteractionConfig {
  type: MicroInteractionType
  trigger?: 'hover' | 'click' | 'focus' | 'manual'
  intensity?: number
  duration?: number
  springConfig?: any
  haptic?: boolean
  sound?: boolean
  onTrigger?: (state: InteractionState) => void
}

// 🚀 Create Cosmic Micro-Interaction
export const createCosmicMicroInteraction = (
  element: () => HTMLElement | undefined,
  config: MicroInteractionConfig
) => {
  const [interactionState, setInteractionState] = createSignal<InteractionState>({
    isActive: false,
    type: config.type,
    intensity: config.intensity || 1,
    duration: config.duration || 300,
    timestamp: 0
  })

  // 🌈 Multi-spring for complex micro-interactions
  const microSpring = createCosmicMultiSpring(
    {
      scale: 1,
      rotation: 0,
      x: 0,
      y: 0,
      opacity: 1,
      brightness: 1,
      hue: 0,
      blur: 0,
      glow: 0
    },
    config.springConfig || getCosmicSpringConfig('spatial', 'fast', 'expressive')
  )

  let timeoutId: number | null = null

  // 🎭 Interaction Patterns
  const getInteractionPattern = (type: MicroInteractionType, intensity: number) => {
    const phi = COSMIC_CONSTANTS.PHI
    const baseIntensity = intensity * 0.1

    switch (type) {
      case 'hover':
        return {
          scale: 1 + baseIntensity,
          brightness: 1 + baseIntensity * 0.5,
          glow: intensity * 0.3
        }
      
      case 'click':
        return {
          scale: 1 - baseIntensity * 0.5,
          brightness: 1 + baseIntensity,
          rotation: intensity * 2
        }
      
      case 'focus':
        return {
          scale: 1 + baseIntensity * 0.5,
          glow: intensity * 0.5,
          hue: 30 * intensity
        }
      
      case 'success':
        return {
          scale: 1 + baseIntensity * 2,
          brightness: 1 + baseIntensity,
          hue: 120, // Green
          glow: intensity * 0.8
        }
      
      case 'error':
        return {
          x: intensity * 5 * Math.sin(Date.now() * 0.05), // Shake effect
          brightness: 1 + baseIntensity,
          hue: 0, // Red
          glow: intensity * 0.6
        }
      
      case 'warning':
        return {
          scale: 1 + baseIntensity,
          brightness: 1 + baseIntensity * 0.8,
          hue: 45, // Orange
          glow: intensity * 0.4
        }
      
      case 'info':
        return {
          scale: 1 + baseIntensity * 0.8,
          brightness: 1 + baseIntensity * 0.6,
          hue: 210, // Blue
          glow: intensity * 0.5
        }
      
      case 'cosmic':
        const cosmicFreq = COSMIC_CONSTANTS.FINE_STRUCTURE / 1000
        return {
          scale: 1 + baseIntensity * Math.sin(Date.now() * cosmicFreq),
          rotation: intensity * 137 / 10, // Fine structure constant
          brightness: 1 + baseIntensity,
          hue: (Date.now() * cosmicFreq) % 360,
          glow: intensity * 0.7
        }
      
      case 'divine':
        return {
          scale: 1 + baseIntensity * phi / 2,
          rotation: intensity * phi * 10,
          brightness: 1 + baseIntensity * phi / 3,
          hue: 280, // Purple
          glow: intensity * phi / 2,
          blur: baseIntensity * 2
        }
      
      default:
        return { scale: 1, opacity: 1 }
    }
  }

  // 🎯 Trigger Interaction
  const trigger = (customIntensity?: number) => {
    const intensity = customIntensity || interactionState().intensity
    const timestamp = Date.now()
    
    setInteractionState(prev => ({
      ...prev,
      isActive: true,
      intensity,
      timestamp
    }))

    const pattern = getInteractionPattern(config.type, intensity)
    microSpring.set(pattern)

    // Haptic feedback
    if (config.haptic && 'vibrate' in navigator) {
      navigator.vibrate(50)
    }

    config.onTrigger?.(interactionState())

    // Auto-reset after duration
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      reset()
    }, interactionState().duration)
  }

  // 🔄 Reset Interaction
  const reset = () => {
    setInteractionState(prev => ({ ...prev, isActive: false }))
    microSpring.set({
      scale: 1,
      rotation: 0,
      x: 0,
      y: 0,
      opacity: 1,
      brightness: 1,
      hue: 0,
      blur: 0,
      glow: 0
    })
  }

  // 🎨 Setup Event Listeners
  createEffect(() => {
    const el = element()
    if (!el || config.trigger === 'manual') return

    const handleTrigger = () => trigger()

    switch (config.trigger) {
      case 'hover':
        el.addEventListener('mouseenter', handleTrigger)
        el.addEventListener('mouseleave', reset)
        break
      case 'click':
        el.addEventListener('click', handleTrigger)
        break
      case 'focus':
        el.addEventListener('focus', handleTrigger)
        el.addEventListener('blur', reset)
        break
    }

    onCleanup(() => {
      el.removeEventListener('mouseenter', handleTrigger)
      el.removeEventListener('mouseleave', reset)
      el.removeEventListener('click', handleTrigger)
      el.removeEventListener('focus', handleTrigger)
      el.removeEventListener('blur', reset)
      if (timeoutId) clearTimeout(timeoutId)
    })
  })

  return {
    interactionState,
    microSpring,
    trigger,
    reset,
    isActive: () => interactionState().isActive
  }
}

// 🌊 Button Micro-Interactions Hook
export const useCosmicButtonMicro = (
  element: () => HTMLElement | undefined,
  variant: 'primary' | 'secondary' | 'success' | 'danger' = 'primary'
) => {
  const hoverInteraction = createCosmicMicroInteraction(element, {
    type: 'hover',
    trigger: 'hover',
    intensity: 1,
    duration: 200
  })

  const clickInteraction = createCosmicMicroInteraction(element, {
    type: 'click',
    trigger: 'click',
    intensity: 1.5,
    duration: 150,
    haptic: true
  })

  return {
    hoverSpring: hoverInteraction.microSpring,
    clickSpring: clickInteraction.microSpring,
    isHovered: hoverInteraction.isActive,
    isClicked: clickInteraction.isActive
  }
}

// 🎯 Form Field Micro-Interactions Hook
export const useCosmicFieldMicro = (
  element: () => HTMLElement | undefined,
  validation?: 'success' | 'error' | 'warning'
) => {
  const focusInteraction = createCosmicMicroInteraction(element, {
    type: 'focus',
    trigger: 'focus',
    intensity: 1,
    duration: 300
  })

  const validationInteraction = createCosmicMicroInteraction(element, {
    type: validation || 'info',
    trigger: 'manual',
    intensity: 1.2,
    duration: 500
  })

  const showValidation = () => {
    if (validation) {
      validationInteraction.trigger()
    }
  }

  return {
    focusSpring: focusInteraction.microSpring,
    validationSpring: validationInteraction.microSpring,
    showValidation,
    isFocused: focusInteraction.isActive
  }
}

// 🌟 Notification Micro-Interactions Hook
export const useCosmicNotificationMicro = (
  type: 'success' | 'error' | 'warning' | 'info' = 'info'
) => {
  const [isVisible, setIsVisible] = createSignal(false)
  
  const notificationInteraction = createCosmicMicroInteraction(() => undefined, {
    type,
    trigger: 'manual',
    intensity: 2,
    duration: 3000,
    onTrigger: () => setIsVisible(true)
  })

  const show = (message?: string, customDuration?: number) => {
    setIsVisible(true)
    notificationInteraction.trigger()
    
    setTimeout(() => {
      setIsVisible(false)
      notificationInteraction.reset()
    }, customDuration || 3000)
  }

  const hide = () => {
    setIsVisible(false)
    notificationInteraction.reset()
  }

  return {
    isVisible,
    notificationSpring: notificationInteraction.microSpring,
    show,
    hide
  }
}

// 🎭 Cosmic Ripple Effect Hook
export const useCosmicRipple = (
  element: () => HTMLElement | undefined
) => {
  const [ripples, setRipples] = createSignal<Array<{
    id: number
    x: number
    y: number
    spring: any
  }>>([])

  const createRipple = (event: MouseEvent) => {
    const el = element()
    if (!el) return

    const rect = el.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    const id = Date.now()

    const rippleSpring = createCosmicMultiSpring(
      { scale: 0, opacity: 0.6 },
      getCosmicSpringConfig('spatial', 'fast', 'expressive')
    )

    setRipples(prev => [...prev, { id, x, y, spring: rippleSpring }])

    // Animate ripple
    rippleSpring.set({ scale: 2, opacity: 0 })

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== id))
    }, 600)
  }

  createEffect(() => {
    const el = element()
    if (!el) return

    el.addEventListener('click', createRipple)
    onCleanup(() => el.removeEventListener('click', createRipple))
  })

  return { ripples }
}

// 🌟 Cosmic Micro-Interaction Presets
export const COSMIC_MICRO_PRESETS = {
  // Button interactions
  primaryButton: {
    hover: { type: 'hover' as MicroInteractionType, intensity: 1.2 },
    click: { type: 'click' as MicroInteractionType, intensity: 1.5, haptic: true }
  },
  
  // Card interactions
  cardHover: {
    type: 'hover' as MicroInteractionType,
    intensity: 0.8,
    duration: 250
  },
  
  // Success feedback
  successFeedback: {
    type: 'success' as MicroInteractionType,
    intensity: 2,
    duration: 800,
    haptic: true
  },
  
  // Error feedback
  errorShake: {
    type: 'error' as MicroInteractionType,
    intensity: 1.5,
    duration: 400,
    haptic: true
  },
  
  // Cosmic interaction
  cosmicPulse: {
    type: 'cosmic' as MicroInteractionType,
    intensity: 1.618, // Golden ratio
    duration: COSMIC_CONSTANTS.FINE_STRUCTURE * 10 // 1370ms
  }
} as const

// 🎨 Micro-Interaction Utilities
export const createMicroCSS = (spring: any, type: MicroInteractionType) => {
  const values = spring.get()
  
  return {
    transform: `
      translateX(${values.x || 0}px) 
      translateY(${values.y || 0}px) 
      scale(${values.scale || 1}) 
      rotate(${values.rotation || 0}deg)
    `.trim(),
    opacity: values.opacity || 1,
    filter: `
      brightness(${values.brightness || 1}) 
      hue-rotate(${values.hue || 0}deg) 
      blur(${values.blur || 0}px)
      drop-shadow(0 0 ${values.glow || 0}px currentColor)
    `.trim()
  }
}

export default {
  createCosmicMicroInteraction,
  useCosmicButtonMicro,
  useCosmicFieldMicro,
  useCosmicNotificationMicro,
  useCosmicRipple,
  COSMIC_MICRO_PRESETS,
  createMicroCSS
}
