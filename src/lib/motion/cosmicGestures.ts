// 🌌 COSMIC GESTURES - Advanced Touch & Mouse Interactions
// Physics-based gesture recognition with spring animations

import { createSignal, createEffect, onCleanup, Accessor } from 'solid-js'
import { createCosmicMultiSpring } from './cosmicSprings'
import { getCosmicSpringConfig, COSMIC_CONSTANTS } from './cosmicMotionScheme'

// 🎯 Gesture Types
export type GestureType = 'swipe' | 'pinch' | 'drag' | 'rotate' | 'tap' | 'longPress'

// 🌟 Gesture State
export interface GestureState {
  isActive: boolean
  startPosition: { x: number; y: number }
  currentPosition: { x: number; y: number }
  velocity: { x: number; y: number }
  distance: number
  angle: number
  scale: number
  rotation: number
  duration: number
}

// 🎨 Gesture Configuration
export interface GestureConfig {
  type: GestureType
  threshold?: number
  sensitivity?: number
  springConfig?: any
  onStart?: (state: GestureState) => void
  onMove?: (state: GestureState) => void
  onEnd?: (state: GestureState) => void
}

// 🚀 Create Cosmic Gesture Handler
export const createCosmicGesture = (
  element: () => HTMLElement | undefined,
  config: GestureConfig
) => {
  const [gestureState, setGestureState] = createSignal<GestureState>({
    isActive: false,
    startPosition: { x: 0, y: 0 },
    currentPosition: { x: 0, y: 0 },
    velocity: { x: 0, y: 0 },
    distance: 0,
    angle: 0,
    scale: 1,
    rotation: 0,
    duration: 0
  })

  let startTime = 0
  let lastPosition = { x: 0, y: 0 }
  let lastTime = 0
  let animationFrame: number | null = null

  // 🎯 Touch Event Handlers
  const handleStart = (e: TouchEvent | MouseEvent) => {
    const point = 'touches' in e ? e.touches[0] : e
    const startPos = { x: point.clientX, y: point.clientY }
    
    startTime = performance.now()
    lastPosition = startPos
    lastTime = startTime

    setGestureState(prev => ({
      ...prev,
      isActive: true,
      startPosition: startPos,
      currentPosition: startPos,
      duration: 0
    }))

    config.onStart?.(gestureState())
  }

  const handleMove = (e: TouchEvent | MouseEvent) => {
    if (!gestureState().isActive) return

    const point = 'touches' in e ? e.touches[0] : e
    const currentPos = { x: point.clientX, y: point.clientY }
    const currentTime = performance.now()
    
    // Calculate velocity
    const deltaTime = currentTime - lastTime
    const deltaX = currentPos.x - lastPosition.x
    const deltaY = currentPos.y - lastPosition.y
    
    const velocity = {
      x: deltaTime > 0 ? deltaX / deltaTime : 0,
      y: deltaTime > 0 ? deltaY / deltaTime : 0
    }

    // Calculate distance and angle
    const startPos = gestureState().startPosition
    const distance = Math.sqrt(
      Math.pow(currentPos.x - startPos.x, 2) + 
      Math.pow(currentPos.y - startPos.y, 2)
    )
    
    const angle = Math.atan2(
      currentPos.y - startPos.y,
      currentPos.x - startPos.x
    ) * (180 / Math.PI)

    setGestureState(prev => ({
      ...prev,
      currentPosition: currentPos,
      velocity,
      distance,
      angle,
      duration: currentTime - startTime
    }))

    lastPosition = currentPos
    lastTime = currentTime

    config.onMove?.(gestureState())
  }

  const handleEnd = (e: TouchEvent | MouseEvent) => {
    if (!gestureState().isActive) return

    setGestureState(prev => ({
      ...prev,
      isActive: false
    }))

    config.onEnd?.(gestureState())
  }

  // 🎨 Setup Event Listeners
  createEffect(() => {
    const el = element()
    if (!el) return

    // Touch events
    el.addEventListener('touchstart', handleStart, { passive: false })
    el.addEventListener('touchmove', handleMove, { passive: false })
    el.addEventListener('touchend', handleEnd, { passive: false })

    // Mouse events
    el.addEventListener('mousedown', handleStart)
    el.addEventListener('mousemove', handleMove)
    el.addEventListener('mouseup', handleEnd)

    onCleanup(() => {
      el.removeEventListener('touchstart', handleStart)
      el.removeEventListener('touchmove', handleMove)
      el.removeEventListener('touchend', handleEnd)
      el.removeEventListener('mousedown', handleStart)
      el.removeEventListener('mousemove', handleMove)
      el.removeEventListener('mouseup', handleEnd)
    })
  })

  return {
    gestureState,
    isActive: () => gestureState().isActive
  }
}

// 🌊 Swipe Gesture Hook
export const useCosmicSwipe = (
  element: () => HTMLElement | undefined,
  onSwipe: (direction: 'left' | 'right' | 'up' | 'down', velocity: number) => void,
  threshold: number = 50
) => {
  return createCosmicGesture(element, {
    type: 'swipe',
    threshold,
    onEnd: (state) => {
      if (state.distance < threshold) return

      const absAngle = Math.abs(state.angle)
      const velocity = Math.sqrt(state.velocity.x ** 2 + state.velocity.y ** 2)

      if (absAngle < 45 || absAngle > 135) {
        // Horizontal swipe
        onSwipe(state.angle > 0 ? 'right' : 'left', velocity)
      } else {
        // Vertical swipe
        onSwipe(state.angle > 0 ? 'down' : 'up', velocity)
      }
    }
  })
}

// 🎯 Drag Gesture Hook
export const useCosmicDrag = (
  element: () => HTMLElement | undefined,
  onDrag: (delta: { x: number; y: number }, velocity: { x: number; y: number }) => void
) => {
  const springs = createCosmicMultiSpring(
    { x: 0, y: 0 },
    getCosmicSpringConfig('spatial', 'fast', 'expressive')
  )

  const gesture = createCosmicGesture(element, {
    type: 'drag',
    onMove: (state) => {
      const delta = {
        x: state.currentPosition.x - state.startPosition.x,
        y: state.currentPosition.y - state.startPosition.y
      }
      
      springs.set({ x: delta.x, y: delta.y })
      onDrag(delta, state.velocity)
    },
    onEnd: () => {
      // Spring back to origin
      springs.set({ x: 0, y: 0 })
    }
  })

  return {
    ...gesture,
    springs,
    position: springs.get
  }
}

// 🌀 Rotation Gesture Hook
export const useCosmicRotation = (
  element: () => HTMLElement | undefined,
  onRotate: (angle: number, velocity: number) => void
) => {
  const rotationSpring = createCosmicMultiSpring(
    { rotation: 0 },
    getCosmicSpringConfig('spatial', 'default', 'expressive')
  )

  let initialAngle = 0

  return createCosmicGesture(element, {
    type: 'rotate',
    onStart: (state) => {
      initialAngle = state.angle
    },
    onMove: (state) => {
      const deltaAngle = state.angle - initialAngle
      const velocity = Math.sqrt(state.velocity.x ** 2 + state.velocity.y ** 2)
      
      rotationSpring.set({ rotation: deltaAngle })
      onRotate(deltaAngle, velocity)
    },
    onEnd: () => {
      rotationSpring.set({ rotation: 0 })
    }
  })
}

// 🎭 Long Press Gesture Hook
export const useCosmicLongPress = (
  element: () => HTMLElement | undefined,
  onLongPress: () => void,
  duration: number = 500
) => {
  let pressTimer: number | null = null

  return createCosmicGesture(element, {
    type: 'longPress',
    onStart: () => {
      pressTimer = setTimeout(() => {
        onLongPress()
      }, duration)
    },
    onMove: (state) => {
      // Cancel if moved too much
      if (state.distance > 10 && pressTimer) {
        clearTimeout(pressTimer)
        pressTimer = null
      }
    },
    onEnd: () => {
      if (pressTimer) {
        clearTimeout(pressTimer)
        pressTimer = null
      }
    }
  })
}

// 🌟 Cosmic Gesture Presets
export const COSMIC_GESTURE_PRESETS = {
  // Card swipe to dismiss
  cardSwipe: {
    threshold: 100,
    springConfig: getCosmicSpringConfig('spatial', 'fast', 'expressive')
  },
  
  // Drawer pull gesture
  drawerPull: {
    threshold: 50,
    sensitivity: 1.5,
    springConfig: getCosmicSpringConfig('spatial', 'default', 'expressive')
  },
  
  // Image zoom/pan
  imageManipulation: {
    threshold: 20,
    sensitivity: 2.0,
    springConfig: getCosmicSpringConfig('spatial', 'slow', 'standard')
  },
  
  // Quick action tap
  quickTap: {
    threshold: 5,
    sensitivity: 0.5,
    springConfig: getCosmicSpringConfig('spatial', 'fast', 'expressive')
  }
} as const

export default {
  createCosmicGesture,
  useCosmicSwipe,
  useCosmicDrag,
  useCosmicRotation,
  useCosmicLongPress,
  COSMIC_GESTURE_PRESETS
}
