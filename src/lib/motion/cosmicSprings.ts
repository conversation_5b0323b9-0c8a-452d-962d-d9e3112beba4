// 🌌 COSMIC SPRINGS - SolidJS Physics-Based Animation Hooks
// Material Design 3 Expressive motion with cosmic design principles

import { createSignal, createEffect, onCleanup, Accessor } from 'solid-js'
import { 
  CosmicSpringConfig, 
  getCosmicSpringConfig, 
  MotionCategory, 
  MotionSpeed, 
  MotionScheme,
  COSMIC_CONSTANTS 
} from './cosmicMotionScheme'

// 🎯 Spring Animation State
export interface SpringState {
  value: number
  velocity: number
  target: number
  isAnimating: boolean
}

// 🌟 Spring Animation Value
export interface SpringValue {
  get: Accessor<number>
  set: (target: number) => void
  stop: () => void
  isAnimating: Accessor<boolean>
}

// 🎨 Multi-Value Spring State
export interface MultiSpringState {
  [key: string]: number
}

export interface MultiSpringValue {
  get: Accessor<MultiSpringState>
  set: (targets: Partial<MultiSpringState>) => void
  stop: () => void
  isAnimating: Accessor<boolean>
}

// 🚀 Create Single Value Spring
export const createCosmicSpring = (
  initialValue: number = 0,
  config?: Partial<CosmicSpringConfig>
): SpringValue => {
  const springConfig: CosmicSpringConfig = {
    tension: 137,
    friction: 26,
    mass: 1,
    velocity: 0,
    precision: 0.01,
    clamp: false,
    ...config
  }

  const [state, setState] = createSignal<SpringState>({
    value: initialValue,
    velocity: springConfig.velocity || 0,
    target: initialValue,
    isAnimating: false
  })

  let animationFrame: number | null = null
  let lastTime = performance.now()

  const animate = (currentTime: number) => {
    const deltaTime = Math.min(currentTime - lastTime, 64) / 1000 // Cap at 64ms for stability
    lastTime = currentTime

    setState(prevState => {
      const { value, velocity, target } = prevState
      const displacement = target - value

      // Spring physics calculations
      const springForce = displacement * springConfig.tension
      const dampingForce = velocity * springConfig.friction
      const acceleration = (springForce - dampingForce) / springConfig.mass!

      const newVelocity = velocity + acceleration * deltaTime
      let newValue = value + newVelocity * deltaTime

      // Apply clamping if enabled
      if (springConfig.clamp) {
        if ((velocity > 0 && newValue > target) || (velocity < 0 && newValue < target)) {
          newValue = target
        }
      }

      // Check if animation should stop
      const isAtRest = 
        Math.abs(displacement) < springConfig.precision! &&
        Math.abs(newVelocity) < springConfig.precision!

      if (isAtRest) {
        return {
          value: target,
          velocity: 0,
          target,
          isAnimating: false
        }
      }

      return {
        value: newValue,
        velocity: newVelocity,
        target,
        isAnimating: true
      }
    })

    if (state().isAnimating) {
      animationFrame = requestAnimationFrame(animate)
    }
  }

  const set = (target: number) => {
    setState(prev => ({ ...prev, target, isAnimating: true }))
    
    if (!animationFrame) {
      lastTime = performance.now()
      animationFrame = requestAnimationFrame(animate)
    }
  }

  const stop = () => {
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
      animationFrame = null
    }
    setState(prev => ({ ...prev, isAnimating: false, velocity: 0 }))
  }

  onCleanup(stop)

  return {
    get: () => state().value,
    set,
    stop,
    isAnimating: () => state().isAnimating
  }
}

// 🌈 Create Multi-Value Spring
export const createCosmicMultiSpring = (
  initialValues: MultiSpringState,
  config?: Partial<CosmicSpringConfig>
): MultiSpringValue => {
  const springConfig: CosmicSpringConfig = {
    tension: 137,
    friction: 26,
    mass: 1,
    velocity: 0,
    precision: 0.01,
    clamp: false,
    ...config
  }

  const keys = Object.keys(initialValues)
  const [state, setState] = createSignal<{
    values: MultiSpringState
    velocities: MultiSpringState
    targets: MultiSpringState
    isAnimating: boolean
  }>({
    values: { ...initialValues },
    velocities: keys.reduce((acc, key) => ({ ...acc, [key]: 0 }), {}),
    targets: { ...initialValues },
    isAnimating: false
  })

  let animationFrame: number | null = null
  let lastTime = performance.now()

  const animate = (currentTime: number) => {
    const deltaTime = Math.min(currentTime - lastTime, 64) / 1000
    lastTime = currentTime

    setState(prevState => {
      const { values, velocities, targets } = prevState
      const newValues: MultiSpringState = {}
      const newVelocities: MultiSpringState = {}
      let anyAnimating = false

      for (const key of keys) {
        const value = values[key]
        const velocity = velocities[key]
        const target = targets[key]
        const displacement = target - value

        // Spring physics
        const springForce = displacement * springConfig.tension
        const dampingForce = velocity * springConfig.friction
        const acceleration = (springForce - dampingForce) / springConfig.mass!

        const newVelocity = velocity + acceleration * deltaTime
        let newValue = value + newVelocity * deltaTime

        // Apply clamping
        if (springConfig.clamp) {
          if ((velocity > 0 && newValue > target) || (velocity < 0 && newValue < target)) {
            newValue = target
          }
        }

        // Check if this property is at rest
        const isAtRest = 
          Math.abs(displacement) < springConfig.precision! &&
          Math.abs(newVelocity) < springConfig.precision!

        if (isAtRest) {
          newValues[key] = target
          newVelocities[key] = 0
        } else {
          newValues[key] = newValue
          newVelocities[key] = newVelocity
          anyAnimating = true
        }
      }

      return {
        values: newValues,
        velocities: newVelocities,
        targets,
        isAnimating: anyAnimating
      }
    })

    if (state().isAnimating) {
      animationFrame = requestAnimationFrame(animate)
    }
  }

  const set = (targets: Partial<MultiSpringState>) => {
    setState(prev => ({
      ...prev,
      targets: { ...prev.targets, ...targets },
      isAnimating: true
    }))
    
    if (!animationFrame) {
      lastTime = performance.now()
      animationFrame = requestAnimationFrame(animate)
    }
  }

  const stop = () => {
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
      animationFrame = null
    }
    setState(prev => ({ 
      ...prev, 
      isAnimating: false,
      velocities: keys.reduce((acc, key) => ({ ...acc, [key]: 0 }), {})
    }))
  }

  onCleanup(stop)

  return {
    get: () => state().values,
    set,
    stop,
    isAnimating: () => state().isAnimating
  }
}

// 🎯 Preset Spring Hooks
export const useCosmicButtonSpring = () => {
  const scale = createCosmicSpring(1, getCosmicSpringConfig('spatial', 'fast', 'expressive'))
  const opacity = createCosmicSpring(1, getCosmicSpringConfig('effects', 'fast', 'expressive'))
  
  return {
    scale,
    opacity,
    hover: () => {
      scale.set(1.05)
      opacity.set(0.9)
    },
    press: () => {
      scale.set(0.95)
      opacity.set(0.8)
    },
    reset: () => {
      scale.set(1)
      opacity.set(1)
    }
  }
}

export const useCosmicCardSpring = () => {
  return createCosmicMultiSpring(
    {
      y: 0,
      scale: 1,
      opacity: 1,
      rotateX: 0,
      rotateY: 0
    },
    getCosmicSpringConfig('spatial', 'default', 'expressive')
  )
}

export const useCosmicModalSpring = () => {
  const backdrop = createCosmicSpring(0, getCosmicSpringConfig('effects', 'default', 'expressive'))
  const content = createCosmicMultiSpring(
    {
      scale: 0.8,
      opacity: 0,
      y: 50
    },
    getCosmicSpringConfig('spatial', 'default', 'expressive')
  )
  
  return {
    backdrop,
    content,
    show: () => {
      backdrop.set(1)
      content.set({ scale: 1, opacity: 1, y: 0 })
    },
    hide: () => {
      backdrop.set(0)
      content.set({ scale: 0.8, opacity: 0, y: 50 })
    }
  }
}

// 🌟 Utility Functions
export const interpolate = (
  value: number,
  inputRange: [number, number],
  outputRange: [number, number],
  clamp: boolean = true
): number => {
  const [inputMin, inputMax] = inputRange
  const [outputMin, outputMax] = outputRange
  
  let result = outputMin + ((value - inputMin) / (inputMax - inputMin)) * (outputMax - outputMin)
  
  if (clamp) {
    result = Math.min(Math.max(result, Math.min(outputMin, outputMax)), Math.max(outputMin, outputMax))
  }
  
  return result
}

export const createStaggeredSprings = (
  count: number,
  initialValue: number = 0,
  config?: Partial<CosmicSpringConfig>,
  staggerDelay: number = COSMIC_CONSTANTS.FINE_STRUCTURE // 137ms
) => {
  const springs = Array.from({ length: count }, () => 
    createCosmicSpring(initialValue, config)
  )
  
  const setStaggered = (target: number) => {
    springs.forEach((spring, index) => {
      setTimeout(() => spring.set(target), index * staggerDelay)
    })
  }
  
  return {
    springs,
    setStaggered,
    setAll: (target: number) => springs.forEach(spring => spring.set(target)),
    stopAll: () => springs.forEach(spring => spring.stop())
  }
}
