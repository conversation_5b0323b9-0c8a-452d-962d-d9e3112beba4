// 🌌 COSMIC LOADING - Advanced Loading Orchestration System
// Physics-based loading animations with cosmic choreography

import { createSignal, createEffect, onCleanup, Accessor } from 'solid-js'
import { createCosmicMultiSpring, createStaggeredSprings } from './cosmicSprings'
import { getCosmicSpringConfig, COSMIC_CONSTANTS } from './cosmicMotionScheme'

// 🎯 Loading Types
export type LoadingType = 
  | 'pulse' | 'wave' | 'orbit' | 'spiral' | 'golden' | 'cosmic' | 'divine' | 'skeleton'

export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// 🌟 Loading Configuration
export interface LoadingConfig {
  type: LoadingType
  duration?: number
  intensity?: number
  particleCount?: number
  springConfig?: any
  colors?: string[]
  onStateChange?: (state: LoadingState) => void
}

// 🚀 Create Cosmic Loading Animation
export const createCosmicLoading = (config: LoadingConfig) => {
  const [loadingState, setLoadingState] = createSignal<LoadingState>('idle')
  const [progress, setProgress] = createSignal(0)
  const [isVisible, setIsVisible] = createSignal(false)

  const duration = config.duration || 2000
  const particleCount = config.particleCount || 8
  
  // 🌈 Multi-spring for complex loading animations
  const loadingSpring = createCosmicMultiSpring(
    {
      scale: 1,
      rotation: 0,
      opacity: 1,
      x: 0,
      y: 0,
      intensity: 0
    },
    config.springConfig || getCosmicSpringConfig('effects', 'default', 'expressive')
  )

  // 🎭 Particle springs for complex animations
  const particleSprings = createStaggeredSprings(
    particleCount,
    0,
    getCosmicSpringConfig('spatial', 'fast', 'expressive'),
    COSMIC_CONSTANTS.FINE_STRUCTURE / particleCount // Stagger based on cosmic constant
  )

  let animationFrame: number | null = null
  let startTime = 0

  // 🎯 Animation Patterns
  const getAnimationPattern = (type: LoadingType, time: number, particleIndex?: number) => {
    const phi = COSMIC_CONSTANTS.PHI
    const normalizedTime = (time % duration) / duration
    
    switch (type) {
      case 'pulse':
        return {
          scale: 1 + 0.3 * Math.sin(normalizedTime * Math.PI * 2),
          opacity: 0.7 + 0.3 * Math.sin(normalizedTime * Math.PI * 2)
        }
      
      case 'wave':
        const waveOffset = (particleIndex || 0) * (Math.PI * 2 / particleCount)
        return {
          y: 20 * Math.sin(normalizedTime * Math.PI * 4 + waveOffset),
          scale: 1 + 0.2 * Math.sin(normalizedTime * Math.PI * 4 + waveOffset)
        }
      
      case 'orbit':
        const angle = normalizedTime * Math.PI * 2 + (particleIndex || 0) * (Math.PI * 2 / particleCount)
        const radius = 30
        return {
          x: radius * Math.cos(angle),
          y: radius * Math.sin(angle),
          rotation: angle * (180 / Math.PI)
        }
      
      case 'spiral':
        const spiralAngle = normalizedTime * Math.PI * 6
        const spiralRadius = 20 + 15 * normalizedTime
        return {
          x: spiralRadius * Math.cos(spiralAngle),
          y: spiralRadius * Math.sin(spiralAngle),
          scale: 1 - 0.5 * normalizedTime,
          opacity: 1 - normalizedTime
        }
      
      case 'golden':
        const goldenAngle = normalizedTime * phi * Math.PI * 2
        const goldenRadius = 25 * Math.pow(normalizedTime, 1/phi)
        return {
          x: goldenRadius * Math.cos(goldenAngle),
          y: goldenRadius * Math.sin(goldenAngle),
          scale: 1 + 0.618 * Math.sin(normalizedTime * Math.PI * 2),
          rotation: goldenAngle * (180 / Math.PI)
        }
      
      case 'cosmic':
        const cosmicFreq = COSMIC_CONSTANTS.FINE_STRUCTURE / 100
        return {
          scale: 1 + 0.4 * Math.sin(normalizedTime * Math.PI * 2 * cosmicFreq),
          rotation: normalizedTime * 360 * cosmicFreq,
          opacity: 0.8 + 0.2 * Math.sin(normalizedTime * Math.PI * 4),
          intensity: Math.sin(normalizedTime * Math.PI * 2)
        }
      
      case 'divine':
        const divinePattern = Math.sin(normalizedTime * Math.PI * 3) * Math.cos(normalizedTime * Math.PI * 5)
        return {
          scale: 1 + 0.3 * divinePattern,
          rotation: normalizedTime * 180 + divinePattern * 45,
          x: 15 * Math.sin(normalizedTime * Math.PI * 2),
          y: 10 * Math.cos(normalizedTime * Math.PI * 3),
          opacity: 0.9 + 0.1 * divinePattern
        }
      
      case 'skeleton':
        return {
          opacity: 0.3 + 0.4 * Math.sin(normalizedTime * Math.PI * 2),
          scale: 1
        }
      
      default:
        return { scale: 1, opacity: 1 }
    }
  }

  // 🎨 Animation Loop
  const animate = (currentTime: number) => {
    if (!startTime) startTime = currentTime
    const elapsed = currentTime - startTime
    
    if (loadingState() === 'loading') {
      const pattern = getAnimationPattern(config.type, elapsed)
      loadingSpring.set(pattern)
      
      // Update particle animations
      for (let i = 0; i < particleCount; i++) {
        const particlePattern = getAnimationPattern(config.type, elapsed, i)
        if (particleSprings.springs[i]) {
          particleSprings.springs[i].set(particlePattern.scale || 1)
        }
      }
      
      setProgress(Math.min(elapsed / duration, 1))
      animationFrame = requestAnimationFrame(animate)
    }
  }

  // 🚀 Control Functions
  const start = () => {
    setLoadingState('loading')
    setIsVisible(true)
    startTime = 0
    config.onStateChange?.('loading')
    animationFrame = requestAnimationFrame(animate)
  }

  const stop = () => {
    setLoadingState('idle')
    setIsVisible(false)
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
      animationFrame = null
    }
    config.onStateChange?.('idle')
  }

  const success = () => {
    setLoadingState('success')
    loadingSpring.set({ scale: 1.2, opacity: 1 })
    config.onStateChange?.('success')
    
    setTimeout(() => {
      loadingSpring.set({ scale: 0, opacity: 0 })
      setTimeout(stop, 300)
    }, 500)
  }

  const error = () => {
    setLoadingState('error')
    loadingSpring.set({ scale: 0.8, opacity: 0.7 })
    config.onStateChange?.('error')
    
    setTimeout(stop, 1000)
  }

  onCleanup(() => {
    if (animationFrame) {
      cancelAnimationFrame(animationFrame)
    }
  })

  return {
    loadingState,
    progress,
    isVisible,
    loadingSpring,
    particleSprings: particleSprings.springs,
    start,
    stop,
    success,
    error,
    isLoading: () => loadingState() === 'loading'
  }
}

// 🌊 Progress Loading Hook
export const useCosmicProgress = (
  initialProgress: number = 0,
  config?: Partial<LoadingConfig>
) => {
  const [progress, setProgress] = createSignal(initialProgress)
  
  const progressSpring = createCosmicMultiSpring(
    { width: initialProgress, opacity: 1 },
    config?.springConfig || getCosmicSpringConfig('effects', 'default', 'standard')
  )

  const updateProgress = (newProgress: number) => {
    setProgress(Math.max(0, Math.min(100, newProgress)))
    progressSpring.set({ width: progress() })
  }

  const complete = () => {
    updateProgress(100)
    setTimeout(() => {
      progressSpring.set({ opacity: 0 })
    }, 500)
  }

  const reset = () => {
    updateProgress(0)
    progressSpring.set({ opacity: 1 })
  }

  return {
    progress,
    progressSpring,
    updateProgress,
    complete,
    reset
  }
}

// 🎭 Skeleton Loading Hook
export const useCosmicSkeleton = (elementCount: number = 3) => {
  const skeletonSprings = createStaggeredSprings(
    elementCount,
    0.3,
    getCosmicSpringConfig('effects', 'slow', 'standard'),
    200
  )

  const show = () => {
    skeletonSprings.setStaggered(1)
  }

  const hide = () => {
    skeletonSprings.setStaggered(0)
  }

  return {
    skeletonSprings: skeletonSprings.springs,
    show,
    hide
  }
}

// 🌟 Cosmic Loading Presets
export const COSMIC_LOADING_PRESETS = {
  // Dashboard loading
  dashboardLoad: {
    type: 'cosmic' as LoadingType,
    duration: 3000,
    particleCount: 8,
    colors: ['#0ea5e9', '#f59e0b', '#d946ef']
  },
  
  // API request loading
  apiRequest: {
    type: 'pulse' as LoadingType,
    duration: 1500,
    intensity: 0.8,
    colors: ['#10b981']
  },
  
  // File upload loading
  fileUpload: {
    type: 'wave' as LoadingType,
    duration: 2000,
    particleCount: 6,
    colors: ['#3b82f6']
  },
  
  // Golden ratio loading
  goldenLoad: {
    type: 'golden' as LoadingType,
    duration: Math.round(COSMIC_CONSTANTS.PHI * 1000), // ~1618ms
    particleCount: Math.round(COSMIC_CONSTANTS.PHI * 5), // ~8
    colors: ['#f59e0b', '#d946ef']
  },
  
  // Divine loading
  divineLoad: {
    type: 'divine' as LoadingType,
    duration: 2500,
    particleCount: 12,
    colors: ['#d946ef', '#8b5cf6', '#06b6d4']
  }
} as const

// 🎨 Loading Utilities
export const createLoadingCSS = (spring: any, type: LoadingType) => {
  const values = spring.get()
  
  const baseTransform = `
    translateX(${values.x || 0}px) 
    translateY(${values.y || 0}px) 
    scale(${values.scale || 1}) 
    rotate(${values.rotation || 0}deg)
  `.trim()

  const baseStyle = {
    transform: baseTransform,
    opacity: values.opacity || 1
  }

  // Type-specific styles
  switch (type) {
    case 'cosmic':
      return {
        ...baseStyle,
        filter: `brightness(${1 + (values.intensity || 0) * 0.5})`
      }
    
    case 'divine':
      return {
        ...baseStyle,
        filter: `hue-rotate(${(values.rotation || 0) * 2}deg)`
      }
    
    default:
      return baseStyle
  }
}

export default {
  createCosmicLoading,
  useCosmicProgress,
  useCosmicSkeleton,
  COSMIC_LOADING_PRESETS,
  createLoadingCSS
}
