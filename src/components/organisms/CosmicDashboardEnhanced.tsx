// 🌌 COSMIC DASHBOARD ENHANCED - Advanced Motion Showcase
// Demonstrates all Phase 2 advanced motion features

import { type Component, createSignal, onMount, Show, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { useDashboardMetrics } from '../../lib/api/hooks'

// Import all advanced motion features
import { useCosmicSwipe, useCosmicDrag, useCosmicLongPress } from '../../lib/motion/cosmicGestures'
import { createCosmicPageTransition, COSMIC_TRANSITION_PRESETS } from '../../lib/motion/cosmicTransitions'
import { createCosmicLoading, COSMIC_LOADING_PRESETS } from '../../lib/motion/cosmicLoading'
import { 
  useCosmicButtonMicro, 
  useCosmicNotificationMicro,
  useCosmicRipple,
  COSMIC_MICRO_PRESETS 
} from '../../lib/motion/cosmicMicroInteractions'

import {
  TrendingUp,
  DollarSign,
  Users,
  Wrench,
  Package,
  Activity,
  Zap,
  Star,
  Heart,
  Sparkles
} from 'lucide-solid'

export const CosmicDashboardEnhanced: Component = () => {
  // 🎯 State Management
  const [selectedCard, setSelectedCard] = createSignal<number | null>(null)
  const [isGestureMode, setIsGestureMode] = createSignal(false)
  const [notificationMessage, setNotificationMessage] = createSignal('')
  
  // 📊 Data
  const dashboardMetrics = useDashboardMetrics()
  
  // 🎭 Refs for gesture interactions
  let dashboardRef: HTMLDivElement | undefined
  let cardRefs: HTMLDivElement[] = []

  // 🌟 Advanced Motion Features
  
  // 1. Page Transition System
  const pageTransition = createCosmicPageTransition({
    type: 'cosmic',
    duration: 800,
    onStart: () => console.log('🚀 Page transition started'),
    onComplete: () => console.log('✅ Page transition completed')
  })

  // 2. Loading Orchestration
  const cosmicLoader = createCosmicLoading({
    ...COSMIC_LOADING_PRESETS.dashboardLoad,
    onStateChange: (state) => console.log('🎭 Loading state:', state)
  })

  const goldenLoader = createCosmicLoading({
    ...COSMIC_LOADING_PRESETS.goldenLoad,
    onStateChange: (state) => console.log('🌟 Golden loading:', state)
  })

  // 3. Notification System
  const successNotification = useCosmicNotificationMicro('success')
  const errorNotification = useCosmicNotificationMicro('error')
  const infoNotification = useCosmicNotificationMicro('info')

  // 4. Gesture Interactions
  const swipeGesture = useCosmicSwipe(
    () => dashboardRef,
    (direction, velocity) => {
      console.log(`🌊 Swiped ${direction} with velocity ${velocity}`)
      infoNotification.show(`Swiped ${direction}!`, 1500)
    },
    100
  )

  const dragGesture = useCosmicDrag(
    () => dashboardRef,
    (delta, velocity) => {
      if (Math.abs(delta.x) > 50 || Math.abs(delta.y) > 50) {
        setIsGestureMode(true)
      }
    }
  )

  const longPressGesture = useCosmicLongPress(
    () => dashboardRef,
    () => {
      console.log('🎯 Long press detected!')
      successNotification.show('Cosmic mode activated!', 2000)
      cosmicLoader.start()
      setTimeout(() => cosmicLoader.success(), 2000)
    },
    800
  )

  // 5. Ripple Effects
  const rippleEffect = useCosmicRipple(() => dashboardRef)

  // 🎨 Enhanced KPI Data
  const enhancedKPIs = () => [
    {
      title: 'Cosmic Revenue',
      value: '$137,618',
      change: '+23.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'cosmic',
      description: 'Universal abundance flowing',
      gesture: 'swipe-right'
    },
    {
      title: 'Divine Customers',
      value: '1,337',
      change: '+12.3%',
      trend: 'up',
      icon: Users,
      color: 'divine',
      description: 'Enlightened user base',
      gesture: 'long-press'
    },
    {
      title: 'Golden Orders',
      value: '618',
      change: '****%',
      trend: 'up',
      icon: Wrench,
      color: 'golden',
      description: 'Perfect ratio achievement',
      gesture: 'drag'
    },
    {
      title: 'Stellar Inventory',
      value: '$89,144',
      change: '-2.1%',
      trend: 'down',
      icon: Package,
      color: 'cosmic',
      description: 'Cosmic stock optimization',
      gesture: 'double-tap'
    }
  ]

  // 🎯 Interactive Functions
  const handleCardInteraction = (index: number, type: string) => {
    setSelectedCard(index)
    
    switch (type) {
      case 'swipe-right':
        pageTransition.executeTransition(true)
        infoNotification.show('Navigating to revenue details...', 2000)
        break
      case 'long-press':
        goldenLoader.start()
        setTimeout(() => {
          goldenLoader.success()
          successNotification.show('Customer insights loaded!', 2000)
        }, 1618) // Golden ratio timing
        break
      case 'drag':
        setIsGestureMode(true)
        infoNotification.show('Drag mode activated!', 1500)
        break
      case 'double-tap':
        errorNotification.show('Feature coming soon!', 2000)
        break
    }
  }

  const resetGestureMode = () => {
    setIsGestureMode(false)
    setSelectedCard(null)
  }

  // 🚀 Lifecycle
  onMount(() => {
    // Initialize with cosmic entrance
    pageTransition.enter()
    
    // Demo sequence
    setTimeout(() => {
      infoNotification.show('🌌 Welcome to Cosmic Dashboard Enhanced!', 3000)
    }, 1000)
  })

  return (
    <div 
      ref={dashboardRef}
      class="relative min-h-screen p-golden-lg space-y-golden-lg overflow-hidden"
      style={pageTransition.pageSpring ? {
        transform: `translateX(${pageTransition.pageSpring.get().x || 0}px) translateY(${pageTransition.pageSpring.get().y || 0}px) scale(${pageTransition.pageSpring.get().scale || 1})`,
        opacity: pageTransition.pageSpring.get().opacity || 1
      } : {}}
    >
      {/* Cosmic Background Effects */}
      <div class="fixed inset-0 pointer-events-none">
        {/* Ripple Effects */}
        <For each={rippleEffect.ripples()}>
          {(ripple) => (
            <div
              class="absolute w-4 h-4 bg-cosmic-400/30 rounded-full pointer-events-none"
              style={{
                left: `${ripple.x}px`,
                top: `${ripple.y}px`,
                transform: `scale(${ripple.spring.get().scale || 0})`,
                opacity: ripple.spring.get().opacity || 0
              }}
            />
          )}
        </For>
        
        {/* Gesture Mode Overlay */}
        <Show when={isGestureMode()}>
          <div class="absolute inset-0 bg-cosmic-900/20 backdrop-blur-sm flex items-center justify-center">
            <div class="text-center text-white">
              <Sparkles size={48} class="mx-auto mb-4 animate-pulse" />
              <h3 class="text-2xl font-bold mb-2">Gesture Mode Active</h3>
              <p class="text-white/70 mb-4">Interact with cosmic gestures</p>
              <GoldenButton 
                variant="cosmic" 
                springMotion={true}
                onClick={resetGestureMode}
              >
                Exit Gesture Mode
              </GoldenButton>
            </div>
          </div>
        </Show>
      </div>

      {/* Header with Advanced Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 class="text-4xl font-bold text-white mb-2 flex items-center">
            <Zap size={32} class="mr-3 text-cosmic-400" />
            Cosmic Dashboard Enhanced
          </h1>
          <p class="text-white/70">
            Experience advanced motion features with cosmic precision
          </p>
        </div>
        
        <div class="flex items-center space-x-4 mt-4 lg:mt-0">
          {/* Loading Demos */}
          <GoldenButton 
            variant="cosmic" 
            size="sm" 
            springMotion={true}
            onClick={() => cosmicLoader.start()}
          >
            <Activity size={16} class="mr-2" />
            Cosmic Load
          </GoldenButton>
          
          <GoldenButton 
            variant="golden" 
            size="sm" 
            springMotion={true}
            onClick={() => goldenLoader.start()}
          >
            <Star size={16} class="mr-2" />
            Golden Load
          </GoldenButton>
          
          <GoldenButton 
            variant="divine" 
            size="sm" 
            springMotion={true}
            onClick={() => setIsGestureMode(!isGestureMode())}
          >
            <Heart size={16} class="mr-2" />
            Gesture Mode
          </GoldenButton>
        </div>
      </div>

      {/* Loading Indicators */}
      <Show when={cosmicLoader.isVisible()}>
        <div class="fixed top-4 right-4 z-50">
          <CosmicCard variant="cosmic" size="sm" glow>
            <div class="flex items-center space-x-3">
              <div 
                class="w-8 h-8 border-2 border-cosmic-400 rounded-full animate-spin"
                style={{
                  transform: `scale(${cosmicLoader.loadingSpring.get().scale || 1})`,
                  opacity: cosmicLoader.loadingSpring.get().opacity || 1
                }}
              />
              <span class="text-white text-sm">Cosmic Loading...</span>
            </div>
          </CosmicCard>
        </div>
      </Show>

      <Show when={goldenLoader.isVisible()}>
        <div class="fixed top-20 right-4 z-50">
          <CosmicCard variant="golden" size="sm" glow>
            <div class="flex items-center space-x-3">
              <div 
                class="w-8 h-8 border-2 border-golden-400 rounded-full"
                style={{
                  transform: `scale(${goldenLoader.loadingSpring.get().scale || 1}) rotate(${goldenLoader.loadingSpring.get().rotation || 0}deg)`,
                  opacity: goldenLoader.loadingSpring.get().opacity || 1
                }}
              />
              <span class="text-white text-sm">Golden Loading...</span>
            </div>
          </CosmicCard>
        </div>
      </Show>

      {/* Enhanced KPI Cards with Gestures */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md">
        <For each={enhancedKPIs()}>
          {(kpi, index) => {
            const Icon = kpi.icon
            return (
              <CosmicCard
                ref={(el) => cardRefs[index()] = el}
                variant="glass"
                size="md"
                glow
                hover3d
                springMotion={true}
                entranceAnimation={true}
                class={`cursor-pointer transition-all duration-300 ${
                  selectedCard() === index() ? 'ring-2 ring-cosmic-400' : ''
                }`}
                onClick={() => handleCardInteraction(index(), kpi.gesture)}
              >
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <Icon size={20} class={`text-${kpi.color}-400`} />
                      <span class="text-white/70 text-sm">{kpi.title}</span>
                    </div>
                    <div class="text-xs text-white/50 bg-white/10 px-2 py-1 rounded">
                      {kpi.gesture}
                    </div>
                  </div>
                  
                  <div class="text-2xl font-bold text-white">{kpi.value}</div>
                  
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-1">
                      <TrendingUp 
                        size={14} 
                        class={kpi.trend === 'up' ? 'text-green-400' : 'text-red-400'} 
                      />
                      <span class={`text-sm ${kpi.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                        {kpi.change}
                      </span>
                    </div>
                  </div>
                  
                  <p class="text-white/50 text-xs">{kpi.description}</p>
                </div>
              </CosmicCard>
            )
          }}
        </For>
      </div>

      {/* Gesture Instructions */}
      <CosmicCard variant="divine" size="lg" glow springMotion={true}>
        <h3 class="text-xl font-bold text-white mb-4 flex items-center">
          <Sparkles size={24} class="mr-3 text-divine-400" />
          Advanced Motion Features Demo
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="text-white font-semibold mb-2">🌊 Gesture Interactions</h4>
            <ul class="text-white/70 text-sm space-y-1">
              <li>• Swipe cards for navigation</li>
              <li>• Long press for cosmic mode</li>
              <li>• Drag for gesture mode</li>
              <li>• Click anywhere for ripples</li>
            </ul>
          </div>
          
          <div>
            <h4 class="text-white font-semibold mb-2">🎭 Loading Orchestration</h4>
            <ul class="text-white/70 text-sm space-y-1">
              <li>• Cosmic loading with physics</li>
              <li>• Golden ratio animations</li>
              <li>• Staggered entrance effects</li>
              <li>• Success/error feedback</li>
            </ul>
          </div>
        </div>
      </CosmicCard>

      {/* Notifications */}
      <Show when={successNotification.isVisible()}>
        <div class="fixed bottom-4 right-4 z-50">
          <CosmicCard variant="cosmic" size="sm" glow>
            <div class="flex items-center space-x-3 text-green-400">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span class="text-sm">Success notification active</span>
            </div>
          </CosmicCard>
        </div>
      </Show>

      <Show when={errorNotification.isVisible()}>
        <div class="fixed bottom-20 right-4 z-50">
          <CosmicCard variant="divine" size="sm" glow>
            <div class="flex items-center space-x-3 text-red-400">
              <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
              <span class="text-sm">Error notification active</span>
            </div>
          </CosmicCard>
        </div>
      </Show>

      <Show when={infoNotification.isVisible()}>
        <div class="fixed bottom-36 right-4 z-50">
          <CosmicCard variant="golden" size="sm" glow>
            <div class="flex items-center space-x-3 text-blue-400">
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              <span class="text-sm">Info notification active</span>
            </div>
          </CosmicCard>
        </div>
      </Show>
    </div>
  )
}

export default CosmicDashboardEnhanced
