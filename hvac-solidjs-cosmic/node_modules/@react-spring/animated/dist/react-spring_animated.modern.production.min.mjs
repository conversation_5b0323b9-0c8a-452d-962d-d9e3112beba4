import{defineHidden as B}from"@react-spring/shared";var y=Symbol.for("Animated:node"),v=e=>!!e&&e[y]===e,k=e=>e&&e[y],D=(e,r)=>B(e,y,r),F=e=>e&&e[y]&&e[y].getPayload(),p=class{constructor(){D(this,this)}getPayload(){return this.payload||[]}};import{is as g}from"@react-spring/shared";var l=class e extends p{constructor(t){super();this._value=t;this.done=!0;this.durationProgress=0;g.num(this._value)&&(this.lastPosition=this._value)}static create(t){return new e(t)}getPayload(){return[this]}getValue(){return this._value}setValue(t,n){return g.num(t)&&(this.lastPosition=t,n&&(t=Math.round(t/n)*n,this.done&&(this.lastPosition=t))),this._value===t?!1:(this._value=t,!0)}reset(){let{done:t}=this;this.done=!1,g.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,t&&(this.lastVelocity=null),this.v0=null)}};import{is as K,createInterpolator as R}from"@react-spring/shared";var c=class e extends l{constructor(t){super(0);this._string=null;this._toString=R({output:[t,t]})}static create(t){return new e(t)}getValue(){let t=this._string;return t??(this._string=this._toString(this._value))}setValue(t){if(K.str(t)){if(t==this._string)return!1;this._string=t,this._value=1}else if(super.setValue(t))this._string=null;else return!1;return!0}reset(t){t&&(this._toString=R({output:[this.getValue(),t]})),this._value=0,super.reset()}};import{isAnimatedString as q}from"@react-spring/shared";import{each as L,eachProp as w,getFluidValue as M,hasFluidValue as C}from"@react-spring/shared";var f={dependencies:null};var d=class extends p{constructor(t){super();this.source=t;this.setValue(t)}getValue(t){let n={};return w(this.source,(a,i)=>{v(a)?n[i]=a.getValue(t):C(a)?n[i]=M(a):t||(n[i]=a)}),n}setValue(t){this.source=t,this.payload=this._makePayload(t)}reset(){this.payload&&L(this.payload,t=>t.reset())}_makePayload(t){if(t){let n=new Set;return w(t,this._addToPayload,n),Array.from(n)}}_addToPayload(t){f.dependencies&&C(t)&&f.dependencies.add(t);let n=F(t);n&&L(n,a=>this.add(a))}};var A=class e extends d{constructor(r){super(r)}static create(r){return new e(r)}getValue(){return this.source.map(r=>r.getValue())}setValue(r){let t=this.getPayload();return r.length==t.length?t.map((n,a)=>n.setValue(r[a])).some(Boolean):(super.setValue(r.map(z)),!0)}};function z(e){return(q(e)?c:l).create(e)}import{is as G,isAnimatedString as J}from"@react-spring/shared";function Le(e){let r=k(e);return r?r.constructor:G.arr(e)?A:J(e)?c:l}import{is as h,eachProp as oe}from"@react-spring/shared";import*as O from"react";import{forwardRef as Q,useRef as H,useCallback as X,useEffect as Y}from"react";import{is as N,each as V,raf as U,useForceUpdate as Z,useOnce as ee,addFluidObserver as te,removeFluidObserver as E,useIsomorphicLayoutEffect as re}from"@react-spring/shared";var x=(e,r)=>{let t=!N.fun(e)||e.prototype&&e.prototype.isReactComponent;return Q((n,a)=>{let i=H(null),o=t&&X(s=>{i.current=ae(a,s)},[a]),[u,T]=ne(n,r),W=Z(),P=()=>{let s=i.current;if(t&&!s)return;(s?r.applyAnimatedValues(s,u.getValue(!0)):!1)===!1&&W()},_=new b(P,T),m=H(void 0);re(()=>(m.current=_,V(T,s=>te(s,_)),()=>{m.current&&(V(m.current.deps,s=>E(s,m.current)),U.cancel(m.current.update))})),Y(P,[]),ee(()=>()=>{let s=m.current;V(s.deps,S=>E(S,s))});let $=r.getComponentProps(u.getValue());return O.createElement(e,{...$,ref:o})})},b=class{constructor(r,t){this.update=r;this.deps=t}eventObserved(r){r.type=="change"&&U.write(this.update)}};function ne(e,r){let t=new Set;return f.dependencies=t,e.style&&(e={...e,style:r.createAnimatedStyle(e.style)}),e=new d(e),f.dependencies=null,[e,t]}function ae(e,r){return e&&(N.fun(e)?e(r):e.current=r),r}var j=Symbol.for("AnimatedComponent"),Ke=(e,{applyAnimatedValues:r=()=>!1,createAnimatedStyle:t=a=>new d(a),getComponentProps:n=a=>a}={})=>{let a={applyAnimatedValues:r,createAnimatedStyle:t,getComponentProps:n},i=o=>{let u=I(o)||"Anonymous";return h.str(o)?o=i[o]||(i[o]=x(o,a)):o=o[j]||(o[j]=x(o,a)),o.displayName=`Animated(${u})`,o};return oe(e,(o,u)=>{h.arr(e)&&(u=I(o)),i[u]=i(o)}),{animated:i}},I=e=>h.str(e)?e:e&&h.str(e.displayName)?e.displayName:h.fun(e)&&e.name||null;export{p as Animated,A as AnimatedArray,d as AnimatedObject,c as AnimatedString,l as AnimatedValue,Ke as createHost,k as getAnimated,Le as getAnimatedType,F as getPayload,v as isAnimated,D as setAnimated};
