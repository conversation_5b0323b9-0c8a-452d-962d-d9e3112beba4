{"name": "@react-spring/shared", "version": "10.0.1", "description": "Globals and shared modules", "module": "./dist/react-spring_shared.legacy-esm.js", "main": "./dist/cjs/index.js", "types": "./dist/react-spring_shared.modern.d.mts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/react-spring_shared.modern.d.mts", "default": "./dist/react-spring_shared.modern.mjs"}, "require": {"types": "./dist/cjs/react-spring_shared.development.d.ts", "default": "./dist/cjs/index.js"}}}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-spring.git"}, "homepage": "https://github.com/pmndrs/react-spring#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)"], "dependencies": {"@react-spring/rafz": "~10.0.1", "@react-spring/types": "~10.0.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "scripts": {"build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "dev": "tsup --watch", "lint": "TIMING=1 eslint \"src/**/*.ts*\"", "pack": "yarn pack"}}