{"version": 3, "file": "index.mjs", "sources": ["../src/index.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nexport function mergeRefs<T = any>(\n  refs: Array<React.MutableRefObject<T> | React.LegacyRef<T> | undefined | null>\n): React.RefCallback<T> {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        (ref as React.MutableRefObject<T | null>).current = value;\n      }\n    });\n  };\n}\n"], "names": ["mergeRefs", "refs", "value", "ref"], "mappings": "AAEgB,SAAAA,EACdC,EACsB,CACtB,OAAQC,GAAU,CAChBD,EAAK,QAASE,GAAQ,CAChB,OAAOA,GAAQ,WACjBA,EAAID,CAAK,EACAC,GAAO,OACfA,EAAyC,QAAUD,EAExD,CAAC,CACH,CACF"}