{"name": "react-merge-refs", "description": "React utility to merge refs.", "version": "2.1.1", "sideEffects": false, "type": "module", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.mjs"}}, "keywords": ["react", "utility", "ref"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "github:gregberge/react-merge-refs", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "scripts": {"build": "rm -rf dist && rollup -c", "format": "prettier --write .", "lint": "eslint . && prettier --check .", "prepublishOnly": "npm run build", "release": "standard-version && conventional-github-releaser --preset angular", "test": "jest"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/eslint-parser": "^7.21.8", "@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.1", "babel-eslint": "^10.1.0", "babel-jest": "^29.5.0", "codecov": "^3.8.3", "conventional-github-releaser": "^3.1.5", "esbuild": "^0.17.19", "eslint": "^8.40.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.21.7", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-esbuild": "^5.0.0", "standard-version": "^9.5.0", "typescript": "^5.0.4"}}