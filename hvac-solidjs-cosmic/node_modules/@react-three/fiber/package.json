{"name": "@react-three/fiber", "version": "8.2.2", "description": "A React renderer for Threejs", "keywords": ["react", "renderer", "fiber", "three", "threejs"], "author": "<PERSON> (https://github.com/drcmda)", "license": "MIT", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)", "<PERSON> (https://github.com/cody<PERSON><PERSON><PERSON>)"], "bugs": {"url": "https://github.com/pmndrs/react-three-fiber/issues"}, "homepage": "https://github.com/pmndrs/react-three-fiber#readme", "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-three-fiber.git"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/react-three-fiber"}, "main": "dist/react-three-fiber.cjs.js", "module": "dist/react-three-fiber.esm.js", "types": "dist/react-three-fiber.cjs.d.ts", "react-native": "native/dist/react-three-fiber-native.cjs.js", "sideEffects": false, "preconstruct": {"entrypoints": ["index.tsx", "native.tsx"]}, "scripts": {"prebuild": "cp ../../readme.md readme.md"}, "dependencies": {"@babel/runtime": "^7.17.8", "@types/react-reconciler": "^0.26.7", "react-reconciler": "^0.27.0", "react-use-measure": "^2.1.1", "scheduler": "^0.21.0", "suspend-react": "^0.0.8", "zustand": "^3.7.1"}, "peerDependencies": {"expo": ">=43.0", "expo-asset": ">=8.4", "expo-gl": ">=11.0", "react": ">=18.0", "react-dom": ">=18.0", "react-native": ">=0.64", "three": ">=0.133"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}, "expo": {"optional": true}, "expo-asset": {"optional": true}, "expo-gl": {"optional": true}}}