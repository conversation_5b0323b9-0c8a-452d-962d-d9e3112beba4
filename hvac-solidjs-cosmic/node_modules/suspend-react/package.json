{"name": "suspend-react", "version": "0.0.8", "description": "Integrate React Suspense into your apps", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "keywords": ["react", "suspense", "resource", "asset"], "author": "<PERSON>", "license": "MIT", "repository": "pmndrs/suspend-react", "homepage": "https://github.com/pmndrs/suspend-react#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "scripts": {"build": "rollup -c", "postbuild": "tsc --emitDeclarationOnly", "prepublishOnly": "npm run build", "test": "echo no tests yet"}, "devDependencies": {"@babel/core": "7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-transform-modules-commonjs": "7.16.0", "@babel/plugin-transform-parameters": "7.16.0", "@babel/plugin-transform-runtime": "7.16.0", "@babel/plugin-transform-template-literals": "7.16.0", "@babel/preset-env": "7.16.0", "@babel/preset-react": "7.16.0", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.6", "@types/jest": "^27.0.2", "@types/node": "^16.11.6", "@types/react": "^17.0.33", "@types/react-dom": "^17.0.10", "@types/react-test-renderer": "^17.0.1", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "eslint": "^8.1.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jest": "^25.2.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^7.0.4", "lint-staged": "^11.2.6", "prettier": "^2.4.1", "react": "^17.0.1", "rollup": "^2.59.0", "rollup-plugin-size-snapshot": "^0.12.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.4.4"}, "peerDependencies": {"react": ">=17.0"}, "dependencies": {}}