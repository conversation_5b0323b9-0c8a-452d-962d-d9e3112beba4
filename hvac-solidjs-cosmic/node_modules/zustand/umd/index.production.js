!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustand={},e.React)}(this,(function(e,t){"use strict";function r(e){var t,r=new Set,n=function(e,n){var u="function"==typeof e?e(t):e;if(u!==t){var c=t;t=n?u:Object.assign({},t,u),r.forEach((function(e){return e(t,c)}))}},u=function(){return t},c={setState:n,getState:u,subscribe:function(e,n,c){return n||c?function(e,n,c){void 0===n&&(n=u),void 0===c&&(c=Object.is),console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");var i=n(t);function o(){var r=n(t);if(!c(i,r)){var u=i;e(i=r,u)}}return r.add(o),function(){return r.delete(o)}}(e,n,c):(r.add(e),function(){return r.delete(e)})},destroy:function(){return r.clear()}};return t=e(n,u,c),c}var n="undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent)?t.useEffect:t.useLayoutEffect;e.default=function(e){var u="function"==typeof e?r(e):e,c=function(e,r){void 0===e&&(e=u.getState),void 0===r&&(r=Object.is);var c,i=t.useReducer((function(e){return e+1}),0)[1],o=u.getState(),a=t.useRef(o),f=t.useRef(e),s=t.useRef(r),d=t.useRef(!1),v=t.useRef();void 0===v.current&&(v.current=e(o));var l=!1;(a.current!==o||f.current!==e||s.current!==r||d.current)&&(c=e(o),l=!r(v.current,c)),n((function(){l&&(v.current=c),a.current=o,f.current=e,s.current=r,d.current=!1}));var b=t.useRef(o);n((function(){var e=function(){try{var e=u.getState(),t=f.current(e);s.current(v.current,t)||(a.current=e,v.current=t,i())}catch(e){d.current=!0,i()}},t=u.subscribe(e);return u.getState()!==b.current&&e(),t}),[]);var g=l?c:v.current;return t.useDebugValue(g),g};return Object.assign(c,u),c[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");var e=[c,u];return{next:function(){var t=e.length<=0;return{value:e.shift(),done:t}}}},c},Object.defineProperty(e,"__esModule",{value:!0})}));
