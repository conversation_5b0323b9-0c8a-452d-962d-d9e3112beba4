{"name": "@types/react-reconciler", "version": "0.26.7", "description": "TypeScript definitions for react-reconciler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-reconciler", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Methuselah96", "githubUsername": "Methuselah96"}, {"name": "<PERSON>", "url": "https://github.com/zhanghaocong", "githubUsername": "zhanghaocong"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-reconciler"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "b03ac7df7430a612cc9999d807920833ebac68fcf0775e804c5e92b628d157c9", "typeScriptVersion": "3.9"}