{"name": "framer-motion-3d", "version": "12.4.13", "description": "A simple and powerful React animation library for @react-three/fiber", "main": "dist/cjs/index.js", "module": "dist/es/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}, "./package.json": "./package.json"}, "types": "dist/index.d.ts", "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/motiondivision/motion/", "sideEffects": false, "keywords": ["react animation", "react", "three", "3d", "pose", "react pose", "animation", "gestures", "drag", "spring", "popmotion", "framer"], "scripts": {"eslint": "yarn run lint", "lint": "yarn eslint src/**/*.ts", "test": "jest --coverage --config jest.config.json --max-workers=2", "build": "yarn clean && tsc -p . && rollup -c", "dev": "yarn watch", "clean": "rm -rf types dist lib", "prettier": "prettier ./src/* --write", "watch": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "prepack": "yarn build", "postpublish": "git push --tags"}, "dependencies": {"framer-motion": "^12.4.13", "react-merge-refs": "^2.0.1"}, "peerDependencies": {"@react-three/fiber": "8.2.2", "react": ">=18.0", "react-dom": ">=18.0", "three": ">=0.133"}, "devDependencies": {"@react-three/fiber": "8.2.2", "@react-three/test-renderer": "^9.0.0", "@rollup/plugin-commonjs": "^22.0.1", "three": "^0.137.0"}, "gitHead": "ac0bc2f6003a20face739983390102b033d291df"}