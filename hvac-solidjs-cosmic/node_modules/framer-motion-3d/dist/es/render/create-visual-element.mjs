import { VisualElement, createBox } from 'framer-motion';
import { setThreeValue } from './utils/set-value.mjs';
import { readThreeValue } from './utils/read-value.mjs';
import { scrapeMotionValuesFromProps } from './utils/scrape-motion-value.mjs';

const createRenderState = () => ({});
class ThreeVisualElement extends VisualElement {
    constructor() {
        super(...arguments);
        this.type = "three";
        this.measureInstanceViewportBox = createBox;
    }
    readValueFromInstance(instance, key) {
        return readThreeValue(instance, key);
    }
    getBaseTargetFromProps() {
        return undefined;
    }
    sortInstanceNodePosition(a, b) {
        return a.id - b.id;
    }
    removeValueFromRenderState() { }
    scrapeMotionValuesFromProps(props, prevProps) {
        return scrapeMotionValuesFromProps(props, prevProps);
    }
    build(state, latestValues) {
        for (const key in latestValues) {
            state[key] = latestValues[key];
        }
    }
    renderInstance(instance, renderState) {
        for (const key in renderState) {
            setThreeValue(instance, key, renderState);
        }
    }
}
const createVisualElement = (_, options) => new ThreeVisualElement(options, {});

export { ThreeVisualElement, createRenderState, createVisualElement };
