"use client";
import { makeUseVisualState, animations, createRendererMotionComponent } from 'framer-motion';
import { createRenderState, createVisualElement } from './create-visual-element.mjs';
import { useRender } from './use-render.mjs';
import { scrapeMotionValuesFromProps } from './utils/scrape-motion-value.mjs';

const useVisualState = makeUseVisualState({
    scrapeMotionValuesFromProps,
    createRenderState,
});
const preloadedFeatures = {
    ...animations,
};
function custom(Component) {
    return createRendererMotionComponent({
        Component,
        preloadedFeatures,
        useRender,
        useVisualState,
        createVisualElement,
    });
}
const componentCache = new Map();
/**
 * @deprecated Motion 3D is deprecated.
 */
const motion = new Proxy(custom, {
    get: (_, key) => {
        !componentCache.has(key) && componentCache.set(key, custom(key));
        return componentCache.get(key);
    },
});

export { motion };
