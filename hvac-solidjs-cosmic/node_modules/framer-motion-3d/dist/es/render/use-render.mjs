import { createElement, useMemo } from 'react';
import { filterProps, isMotionValue, resolveMotionValue } from 'framer-motion';
import { useHover } from './gestures/use-hover.mjs';
import { useTap } from './gestures/use-tap.mjs';

const useRender = (Component, props, ref, _state, isStatic, visualElement) => {
    const visualProps = useVisualProps(props);
    /**
     * If isStatic, render motion values as props
     * If !isStatic, render motion values as props on initial render
     */
    return createElement(Component, {
        ref,
        ...filterProps(props, false, false),
        ...visualProps,
        onUpdate: props.onInstanceUpdate,
        ...useHover(isStatic, props, visualElement),
        ...useTap(isStatic, props, visualElement),
    });
};
function useVisualProps(props) {
    return useMemo(() => {
        const visualProps = {};
        for (const key in props) {
            const prop = props[key];
            if (isMotionValue(prop)) {
                visualProps[key] = prop.get();
            }
            else if (Array.isArray(prop) && prop.includes(isMotionValue)) {
                visualProps[key] = prop.map(resolveMotionValue);
            }
        }
        return visualProps;
    }, []);
}

export { useRender };
