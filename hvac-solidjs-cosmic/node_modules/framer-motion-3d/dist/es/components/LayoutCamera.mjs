"use client";
import { jsx } from 'react/jsx-runtime';
import { extend } from '@react-three/fiber';
import { forwardRef } from 'react';
import { mergeRefs } from 'react-merge-refs';
import { PerspectiveCamera } from 'three';
import { motion } from '../render/motion.mjs';
import { useLayoutCamera } from './use-layout-camera.mjs';

extend({ PerspectiveCamera: PerspectiveCamera });
/**
 * Adapted from https://github.com/pmndrs/drei/blob/master/src/core/PerspectiveCamera.tsx
 *
 * @deprecated Motion 3D is deprecated.
 */
const LayoutCamera = forwardRef((props, ref) => {
    const { cameraRef } = useLayoutCamera(props, (size) => {
        const { current: cam } = cameraRef;
        if (cam && !props.manual) {
            cam.aspect = size.width / size.height;
            cam.updateProjectionMatrix();
        }
    });
    return (jsx(motion.perspectiveCamera, { ref: mergeRefs([cameraRef, ref]), ...props }));
});

export { LayoutCamera };
