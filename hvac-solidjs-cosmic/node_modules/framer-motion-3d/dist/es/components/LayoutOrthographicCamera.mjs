"use client";
import { jsx } from 'react/jsx-runtime';
import { extend } from '@react-three/fiber';
import { forwardRef } from 'react';
import { mergeRefs } from 'react-merge-refs';
import { OrthographicCamera } from 'three';
import { motion } from '../render/motion.mjs';
import { useLayoutCamera } from './use-layout-camera.mjs';

extend({ OrthographicCamera: OrthographicCamera });
/**
 * @deprecated Motion 3D is deprecated.
 */
const LayoutOrthographicCamera = forwardRef((props, ref) => {
    const { size, cameraRef } = useLayoutCamera(props, (newSize) => {
        const { current: cam } = cameraRef;
        if (cam) {
            cam.left = newSize.width / -2;
            cam.right = newSize.width / 2;
            cam.top = newSize.height / 2;
            cam.bottom = newSize.height / -2;
            cam.updateProjectionMatrix();
        }
    });
    return (jsx(motion.orthographicCamera, { left: size.width / -2, right: size.width / 2, top: size.height / 2, bottom: size.height / -2, ref: mergeRefs([cameraRef, ref]), ...props }));
});

export { LayoutOrthographicCamera };
