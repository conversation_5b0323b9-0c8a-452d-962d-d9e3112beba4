/// <reference types="react" />
import * as react from 'react';
import { JSX, ForwardRefExoticComponent, PropsWithoutRef, RefAttributes } from 'react';
import { MeshProps, Vector3, Euler, Color, Props as Props$2 } from '@react-three/fiber';
import * as framer_motion from 'framer-motion';
import { AnimationLifecycles, AnimationProps, TapHandlers, HoverHandlers, MotionValue } from 'framer-motion';

interface ThreeMotionProps extends AnimationLifecycles, AnimationProps, TapHandlers, HoverHandlers {
    onInstanceUpdate?: MeshProps["onUpdate"];
}
/**
 * @public
 */
type ForwardRefComponent<T, P> = ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<T>>;
type MotionValueOrNumber = number | MotionValue<number>;
type MotionValueVector3 = [
    MotionValueOrNumber,
    MotionValueOrNumber,
    MotionValueOrNumber
];
type AcceptMotionValues<T> = Omit<T, "position" | "scale" | "rotation" | "color"> & {
    position?: Vector3 | MotionValueVector3 | MotionValueOrNumber;
    scale?: Vector3 | MotionValueVector3 | MotionValueOrNumber;
    rotation?: Euler | MotionValueVector3 | MotionValueOrNumber;
    color?: Color | MotionValue<string>;
};
/**
 * Motion-optimised versions of React's HTML components.
 *
 * @public
 */
type ThreeMotionComponents = {
    [K in keyof JSX.IntrinsicElements]: ForwardRefComponent<JSX.IntrinsicElements[K], ThreeMotionProps & Omit<AcceptMotionValues<JSX.IntrinsicElements[K]>, "onUpdate" | "transition">>;
};

declare function custom<Props extends {}>(Component: string): react.ForwardRefExoticComponent<react.RefAttributes<unknown>>;
/**
 * @deprecated Motion 3D is deprecated.
 */
declare const motion: typeof custom & ThreeMotionComponents;

interface MotionCanvasProps extends Omit<Props$2, "resize"> {
}
/**
 * @deprecated Motion 3D is deprecated.
 */
declare const MotionCanvas: react.ForwardRefExoticComponent<MotionCanvasProps & react.RefAttributes<HTMLCanvasElement>>;

interface LayoutCameraProps {
    makeDefault?: boolean;
    manual?: boolean;
    children?: React.ReactNode;
}

type Props$1 = JSX.IntrinsicElements["perspectiveCamera"] & LayoutCameraProps & ThreeMotionProps;
/**
 * Adapted from https://github.com/pmndrs/drei/blob/master/src/core/PerspectiveCamera.tsx
 *
 * @deprecated Motion 3D is deprecated.
 */
declare const LayoutCamera: react.ForwardRefExoticComponent<Omit<Props$1, "ref"> & react.RefAttributes<unknown>>;

type Props = JSX.IntrinsicElements["orthographicCamera"] & LayoutCameraProps & ThreeMotionProps;
/**
 * @deprecated Motion 3D is deprecated.
 */
declare const LayoutOrthographicCamera: react.ForwardRefExoticComponent<Omit<Props, "ref"> & react.RefAttributes<unknown>>;

/**
 * @deprecated Motion 3D is deprecated.
 */
declare function useTime(): framer_motion.MotionValue<number>;

export { LayoutCamera, LayoutOrthographicCamera, MotionCanvas, type MotionCanvasProps, motion, useTime };
