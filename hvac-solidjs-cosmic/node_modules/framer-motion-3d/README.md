<p align="center">
  <img src="https://user-images.githubusercontent.com/22095598/123793419-f5528800-d8e1-11eb-8c5f-e2dad45a9c81.png" width="108" height="108" alt="Motion" />
</p>
<h1 align="center">Motion 3D</h1>
<h3 align="center">
  An open source motion library for React Three Fiber.
</h3>
<h3 align="center">Motion powers Framer, the web builder for creative pros. Design and ship your dream site. Zero code, maximum speed.
</h3>
<br/>
<p align="center">
  <a href="https://www.framer.com?utm_source=motion-readme">
    <img src="https://framerusercontent.com/images/atXqxn4JhKm4LXVncdNjkKV7yCU.png" width="140" alt="Start for free" />
  </a>
</p>
<br/>
<p align="center">
  <a href="https://www.framer.com?utm_source=motion-readme">
    <img src="https://framerusercontent.com/images/pMSOmGP2V8sSaZRV2D7i4HTBTe4.png" width="1000" alt="Framer Banner" />
  </a>
</p>

<br>

<p align="center">
  <a href="https://www.npmjs.com/package/framer-motion-3d" target="_blank">
    <img src="https://img.shields.io/npm/v/framer-motion-3d.svg?style=flat-square" />
  </a>
  <a href="https://www.npmjs.com/package/framer-motion-3d" target="_blank">
  <img src="https://img.shields.io/npm/dm/framer-motion-3d.svg?style=flat-square" />
  </a>
  <a href="https://twitter.com/motiondotdev" target="_blank">
  <img src="https://img.shields.io/twitter/follow/framer.svg?style=social&label=Follow"  />
  </a>
</p>

<br>

It looks like this:

```jsx
<motion.boxGeometry animate={{ x: 0 }} />
```

It does all this:

-   Springs
-   Keyframes
-   Gestures (tap/hover)
-   Exit animations
-   Orchestrate animations across components

...and a whole lot more.

### 📚 Docs

Check out [our documentation](https://www.framer.com/docs/three-introduction/?utm_source=motion-readme-docs) for guides and a full API reference.

Or check out [our examples](https://www.framer.com/docs/three-introduction/#examples?utm_source=motion-readme-docs) for inspiration.

### 🛠 Contribute

Want to contribute to Motion 3D? Our [contributing guide](https://github.com/motiondivision/motion/blob/master/CONTRIBUTING.md) has you covered.

### 👩🏻‍⚖️ License

Motion 3D is MIT licensed.

## Framer

Get on the same page as your designers before production. Get started with [design and prototyping in Framer](https://www.framer.com/?utm_source=motion-readme).
