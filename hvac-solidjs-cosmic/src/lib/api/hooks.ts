// 🌌 COSMIC API HOOKS - SolidJS Reactive Integration
// Perfect reactive data management with TanStack Query

import { createQuery, createMutation, useQueryClient } from '@tanstack/solid-query'
import { createSignal, createEffect } from 'solid-js'
import { cosmicAPI } from './client'
// 🎯 Type definitions for enhanced cosmic integration
type Customer = {
  id: string
  name: string
  email: string
  phone: string
  address: string
  status: string
  aiScore: number
  lifetimeValue: number
  lastContact: string
  equipmentCount: number
  serviceHistory: number
  riskLevel: string
  nextMaintenance: string
}

type ServiceJob = {
  id: string
  customerId: string
  customerName: string
  title: string
  description: string
  status: string
  priority: string
  assignedTo: string
  scheduledDate: string
  estimatedHours: number
  actualHours: number
  equipmentNeeded: string[]
  aiPriorityScore: number
}

type CreateCustomerInput = Omit<Customer, 'id' | 'aiScore' | 'lifetimeValue' | 'lastContact' | 'equipmentCount' | 'serviceHistory' | 'riskLevel' | 'nextMaintenance'>
type UpdateCustomerInput = Partial<Customer> & { id: string }
type CreateJobInput = Omit<ServiceJob, 'id' | 'aiPriorityScore'>

type DashboardMetrics = {
  totalRevenue: number
  revenueGrowth: number
  totalCustomers: number
  customerGrowth: number
  activeJobs: number
  jobsGrowth: number
  inventoryValue: number
  inventoryGrowth: number
}

type SearchFilters = {
  query?: string
  status?: string[]
  dateFrom?: string
  dateTo?: string
  tags?: string[]
  assignedTo?: string
}

type PaginationParams = {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

type PaginatedResponse<T> = {
  data: T[]
  total: number
  page: number
  limit: number
}

// 🏢 Customer Hooks
export const useCustomers = (filters?: SearchFilters, pagination?: PaginationParams) => {
  return createQuery(() => ({
    queryKey: ['customers', filters, pagination],
    queryFn: () => cosmicAPI.customer.list.query({ filters, pagination }),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  }))
}

export const useCustomer = (id: string) => {
  return createQuery(() => ({
    queryKey: ['customer', id],
    queryFn: () => cosmicAPI.customer.get.query({ id }),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  }))
}

export const useCreateCustomer = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (input: CreateCustomerInput) => cosmicAPI.customer.create.mutate(input),
    onSuccess: (newCustomer) => {
      // Optimistic update - add to customers list
      queryClient.setQueryData(['customers'], (old: PaginatedResponse<Customer> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: [newCustomer, ...old.data],
          total: old.total + 1
        }
      })
      
      // Invalidate customers list to refetch
      queryClient.invalidateQueries({ queryKey: ['customers'] })
    },
    onError: (error) => {
      console.error('Failed to create customer:', error)
      // Could show toast notification here
    }
  }))
}

export const useDeleteJob = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (id: string) => cosmicAPI.job.delete.mutate({ id }),
    onSuccess: (_, deletedId) => {
      // Remove from jobs list
      queryClient.setQueryData(['jobs'], (old: PaginatedResponse<ServiceJob> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: old.data.filter(job => job.id !== deletedId),
          total: old.total - 1
        }
      })
      
      // Remove specific job cache
      queryClient.removeQueries({ queryKey: ['job', deletedId] })
      queryClient.invalidateQueries({ queryKey: ['analytics'] }) // Jobs affect analytics
    }
  }))
}

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (input: UpdateCustomerInput) => cosmicAPI.customer.update.mutate(input),
    onSuccess: (updatedCustomer) => {
      // Update specific customer in cache
      queryClient.setQueryData(['customer', updatedCustomer.id], updatedCustomer)
      
      // Update customer in customers list
      queryClient.setQueryData(['customers'], (old: PaginatedResponse<Customer> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: old.data.map(customer => 
            customer.id === updatedCustomer.id ? updatedCustomer : customer
          )
        }
      })
    }
  }))
}

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (id: string) => cosmicAPI.customer.delete.mutate({ id }),
    onSuccess: (_, deletedId) => {
      // Remove from customers list
      queryClient.setQueryData(['customers'], (old: PaginatedResponse<Customer> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: old.data.filter(customer => customer.id !== deletedId),
          total: old.total - 1
        }
      })
      
      // Remove specific customer cache
      queryClient.removeQueries({ queryKey: ['customer', deletedId] })
    }
  }))
}

// 🔧 Service Job Hooks
export const useJobs = (filters?: SearchFilters, pagination?: PaginationParams) => {
  return createQuery(() => ({
    queryKey: ['jobs', filters, pagination],
    queryFn: () => cosmicAPI.job.list.query({ filters, pagination }),
    staleTime: 1 * 60 * 1000, // 1 minute (jobs change more frequently)
  }))
}

export const useJob = (id: string) => {
  return createQuery(() => ({
    queryKey: ['job', id],
    queryFn: () => cosmicAPI.job.get.query({ id }),
    enabled: !!id,
  }))
}

export const useCreateJob = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (input: CreateJobInput) => cosmicAPI.job.create.mutate(input),
    onSuccess: (newJob) => {
      queryClient.setQueryData(['jobs'], (old: PaginatedResponse<ServiceJob> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: [newJob, ...old.data],
          total: old.total + 1
        }
      })
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
      queryClient.invalidateQueries({ queryKey: ['analytics'] }) // Jobs affect analytics
    }
  }))
}

export const useUpdateJob = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (input: Partial<ServiceJob> & { id: string }) => cosmicAPI.job.update.mutate(input),
    onSuccess: (updatedJob) => {
      queryClient.setQueryData(['job', updatedJob.id], updatedJob)
      queryClient.setQueryData(['jobs'], (old: PaginatedResponse<ServiceJob> | undefined) => {
        if (!old) return old
        return {
          ...old,
          data: old.data.map(job => job.id === updatedJob.id ? updatedJob : job)
        }
      })
      queryClient.invalidateQueries({ queryKey: ['analytics'] })
    }
  }))
}

// 📊 Analytics Hooks
export const useDashboardMetrics = () => {
  return createQuery(() => ({
    queryKey: ['analytics', 'dashboard'],
    queryFn: () => cosmicAPI.analytics.dashboard.query(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  }))
}

export const useRevenueData = (dateFrom: string, dateTo: string) => {
  return createQuery(() => ({
    queryKey: ['analytics', 'revenue', dateFrom, dateTo],
    queryFn: () => cosmicAPI.analytics.revenue.query({ dateFrom, dateTo }),
    enabled: !!dateFrom && !!dateTo,
    staleTime: 5 * 60 * 1000, // 5 minutes
  }))
}

// 🧠 AI Analysis Hooks
export const useAnalyzeEmail = () => {
  const queryClient = useQueryClient()
  
  return createMutation(() => ({
    mutationFn: (emailId: string) => cosmicAPI.ai.analyzeEmail.mutate({ emailId }),
    onSuccess: (analysis) => {
      // Update email with analysis
      queryClient.invalidateQueries({ queryKey: ['emails'] })
    }
  }))
}

export const useAnalyzeText = () => {
  return createMutation(() => ({
    mutationFn: ({ text, context }: { text: string; context?: string }) => 
      cosmicAPI.ai.analyzeText.mutate({ text, context }),
  }))
}

// 🌟 Real-time Updates Hook
export const useRealTimeUpdates = () => {
  const [isConnected, setIsConnected] = createSignal(false)
  const [lastUpdate, setLastUpdate] = createSignal<Date | null>(null)
  const queryClient = useQueryClient()

  createEffect(() => {
    // This would set up WebSocket subscriptions for real-time updates
    // Implementation depends on backend WebSocket setup
    
    const handleUpdate = (data: any) => {
      setLastUpdate(new Date())
      
      // Invalidate relevant queries based on update type
      switch (data.type) {
        case 'customer_updated':
          queryClient.invalidateQueries({ queryKey: ['customers'] })
          queryClient.invalidateQueries({ queryKey: ['customer', data.id] })
          break
        case 'job_updated':
          queryClient.invalidateQueries({ queryKey: ['jobs'] })
          queryClient.invalidateQueries({ queryKey: ['job', data.id] })
          queryClient.invalidateQueries({ queryKey: ['analytics'] })
          break
        case 'email_processed':
          queryClient.invalidateQueries({ queryKey: ['emails'] })
          break
      }
    }

    // Set up WebSocket connection here
    setIsConnected(true)

    return () => {
      // Cleanup WebSocket connection
      setIsConnected(false)
    }
  })

  return {
    isConnected,
    lastUpdate
  }
}

// 🎯 Utility Hooks
export const useInvalidateAll = () => {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.invalidateQueries()
  }
}

export const usePrefetchCustomer = () => {
  const queryClient = useQueryClient()
  
  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: ['customer', id],
      queryFn: () => cosmicAPI.customer.get.query({ id }),
      staleTime: 5 * 60 * 1000,
    })
  }
}

// 🌌 Export all hooks for cosmic integration
