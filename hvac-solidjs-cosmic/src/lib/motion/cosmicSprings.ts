// 🌌 COSMIC SPRINGS - M3 Expressive Motion Implementation
// Physics-based spring animations with golden ratio constants

import { createSignal, createEffect } from 'solid-js'

// 🎯 Golden Ratio Spring Constants (φ = 1.618)
const COSMIC_SPRING_CONFIG = {
  // Standard springs
  gentle: { tension: 120, friction: 14 },
  wobbly: { tension: 180, friction: 12 },
  stiff: { tension: 210, friction: 20 },

  // Cosmic springs (golden ratio based)
  cosmic: { tension: 161.8, friction: 13.7 }, // φ * 100, 137 (fine structure constant)
  golden: { tension: 261.8, friction: 16.18 }, // φ² * 100, φ * 10
  divine: { tension: 423.6, friction: 23.6 }, // φ³ * 100, φ * 14.6
}

// 🌟 Spring Animation State
interface SpringState {
  x: number
  y: number
  scale: number
  opacity: number
  rotateX: number
  rotateY: number
  rotateZ: number
}

const defaultState: SpringState = {
  x: 0,
  y: 0,
  scale: 1,
  opacity: 1,
  rotateX: 0,
  rotateY: 0,
  rotateZ: 0
}

// 🎭 Simple Spring Animation Hook
export const useCosmicCardSpring = () => {
  const [state, setState] = createSignal<SpringState>(defaultState)

  return {
    get: () => state(),
    set: (newState: Partial<SpringState>) => {
      setState(prev => ({ ...prev, ...newState }))
    }
  }
}

export const useCosmicButtonSpring = () => {
  const [state, setState] = createSignal<SpringState>(defaultState)

  return {
    get: () => state(),
    set: (newState: Partial<SpringState>) => {
      setState(prev => ({ ...prev, ...newState }))
    }
  }
}

// 🚀 Advanced Spring Hook with Physics
export const useCosmicSpring = (config: keyof typeof COSMIC_SPRING_CONFIG = 'cosmic') => {
  const [state, setState] = createSignal<SpringState>(defaultState)
  const [target, setTarget] = createSignal<SpringState>(defaultState)

  // Simple spring animation (for now - can be enhanced with real physics later)
  createEffect(() => {
    const targetState = target()
    const currentState = state()

    // Animate towards target with easing
    const animate = () => {
      const newState = { ...currentState }
      const factor = 0.1 // Simple easing factor

      Object.keys(targetState).forEach(key => {
        const k = key as keyof SpringState
        newState[k] = currentState[k] + (targetState[k] - currentState[k]) * factor
      })

      setState(newState)
    }

    // Start animation
    const animationId = requestAnimationFrame(animate)

    return () => cancelAnimationFrame(animationId)
  })

  return {
    get: () => state(),
    set: (newState: Partial<SpringState>) => {
      setTarget(prev => ({ ...prev, ...newState }))
    },
    config: COSMIC_SPRING_CONFIG[config]
  }
}
