import { type Component, createSignal, onMount, For, Show, createMemo, createEffect } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { CustomerDetailsModal } from '../organisms/CustomerDetailsModal'
import { CustomerAnalyticsDashboard } from '../organisms/CustomerAnalyticsDashboard'
import {
  Users,
  Search,
  Plus,
  Mail,
  Phone,
  MapPin,
  Edit,
  Trash2,
  Eye,
  Filter,
  Download,
  Building,
  Tag,
  Activity,
  MoreVertical,
  UserPlus,
  Upload,
  Zap,
  Brain,
  Target,
  Star,
  TrendingUp,
  Calendar,
  RefreshCw,
  MessageCircle,
  Video,
  Globe,
  Award,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Sparkles,
  Heart,
  Shield,
  Bookmark,
  Share2,
  Copy,
  ExternalLink,
  Settings,
  BarChart3,
  PieChart,
  TrendingDown,
  Layers,
  Zap as Lightning,
  Cpu,
  Database,
  Wifi,
  Signal
} from 'lucide-solid'
import { useCustomers, useCreateCustomer, useDeleteCustomer, apiUtils } from '../../lib/api'
import type { Customer, SearchFilters, PaginationParams } from '../../lib/api'
import { toast } from '../molecules/CosmicToast'

export const CustomersPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedStatus, setSelectedStatus] = createSignal('all')
  const [showAddModal, setShowAddModal] = createSignal(false)
  const [selectedCustomerId, setSelectedCustomerId] = createSignal<string | null>(null)
  const [showDetailsModal, setShowDetailsModal] = createSignal(false)
  const [currentPage, setCurrentPage] = createSignal(1)
  const [pageSize, setPageSize] = createSignal(20)
  const [sortBy, setSortBy] = createSignal('name')
  const [sortOrder, setSortOrder] = createSignal<'asc' | 'desc'>('asc')

  // Enhanced filtering and search
  const [selectedTags, setSelectedTags] = createSignal<string[]>([])
  const [selectedLocation, setSelectedLocation] = createSignal('all')
  const [selectedRating, setSelectedRating] = createSignal('all')
  const [showAdvancedFilters, setShowAdvancedFilters] = createSignal(false)
  const [viewMode, setViewMode] = createSignal<'grid' | 'list'>('list')
  const [selectedCustomers, setSelectedCustomers] = createSignal<string[]>([])
  const [showBulkActions, setShowBulkActions] = createSignal(false)

  // AI and analytics
  const [aiInsights, setAiInsights] = createSignal(true)
  const [showCustomerAnalytics, setShowCustomerAnalytics] = createSignal(false)
  const [realtimeUpdates, setRealtimeUpdates] = createSignal(true)

  // 🌟 Real API Integration
  const filters = createMemo((): SearchFilters =>
    apiUtils.createFilters({
      query: searchTerm(),
      status: selectedStatus() === 'all' ? undefined : [selectedStatus()]
    })
  )

  const pagination = createMemo((): PaginationParams =>
    apiUtils.createPagination({
      page: currentPage(),
      limit: pageSize(),
      sortBy: sortBy(),
      sortOrder: sortOrder()
    })
  )

  const customersQuery = useCustomers(filters(), pagination())
  const createCustomerMutation = useCreateCustomer()
  const deleteCustomerMutation = useDeleteCustomer()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  // 🔄 Auto-refresh when filters change
  createEffect(() => {
    if (searchTerm() || selectedStatus()) {
      setCurrentPage(1) // Reset to first page when filtering
    }
  })

  // 🎯 Customer Actions
  const handleDeleteCustomer = async (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      try {
        await deleteCustomerMutation.mutateAsync(customerId)
        toast.success('Customer deleted successfully!')
      } catch (error) {
        toast.error(apiUtils.formatError(error))
      }
    }
  }

  const handleCreateCustomer = async (customerData: any) => {
    try {
      await createCustomerMutation.mutateAsync(customerData)
      toast.success('Customer created successfully!')
      setShowAddModal(false)
    } catch (error) {
      toast.error(apiUtils.formatError(error))
    }
  }

  const handleViewCustomer = (customerId: string) => {
    setSelectedCustomerId(customerId)
    setShowDetailsModal(true)
  }

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false)
    setSelectedCustomerId(null)
  }

  // 📊 Dynamic customer data from API or fallback
  const customers = () => customersQuery.data?.data || []
  const totalCustomers = () => customersQuery.data?.total || 0
  const totalPages = () => customersQuery.data?.totalPages || 1

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500'
      case 'pending':
        return 'bg-yellow-500'
      case 'inactive':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  // 🧠 AI-Powered Customer Insights
  const getCustomerScore = (customer: Customer) => {
    // Simple scoring algorithm - in real app this would come from AI service
    const recencyScore = new Date(customer.updatedAt).getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000 ? 30 : 10
    const statusScore = customer.status === 'active' ? 40 : customer.status === 'prospect' ? 20 : 10
    const engagementScore = Math.min((customer.tags?.length || 0) * 10, 30)
    return Math.min(recencyScore + statusScore + engagementScore, 100)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  // 🎨 Enhanced UI Helper Functions
  const getCustomerAvatar = (customer: Customer) => {
    const initials = customer.name.split(' ').map(n => n[0]).join('').toUpperCase()
    const colors = [
      'from-cosmic-400 to-divine-400',
      'from-golden-400 to-cosmic-400',
      'from-divine-400 to-golden-400',
      'from-purple-400 to-pink-400',
      'from-blue-400 to-cyan-400',
      'from-green-400 to-emerald-400',
      'from-orange-400 to-red-400',
      'from-indigo-400 to-purple-400'
    ]
    const colorIndex = customer.name.charCodeAt(0) % colors.length
    return { initials, gradient: colors[colorIndex] }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle
      case 'inactive': return XCircle
      case 'pending': return AlertCircle
      default: return Clock
    }
  }

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => i < rating)
  }

  const toggleCustomerSelection = (customerId: string) => {
    const selected = selectedCustomers()
    if (selected.includes(customerId)) {
      setSelectedCustomers(selected.filter(id => id !== customerId))
    } else {
      setSelectedCustomers([...selected, customerId])
    }
  }

  const selectAllCustomers = () => {
    if (selectedCustomers().length === customers().length) {
      setSelectedCustomers([])
    } else {
      setSelectedCustomers(customers().map(c => c.id))
    }
  }

  const getCustomerInsights = (customer: Customer) => {
    const score = getCustomerScore(customer)
    const insights = []

    if (score >= 80) {
      insights.push({ type: 'success', text: 'High-value customer', icon: Star })
    } else if (score >= 60) {
      insights.push({ type: 'warning', text: 'Potential growth', icon: TrendingUp })
    } else {
      insights.push({ type: 'info', text: 'Needs attention', icon: AlertCircle })
    }

    if (customer.status === 'active') {
      insights.push({ type: 'success', text: 'Active engagement', icon: Activity })
    }

    return insights
  }

  return (
    <div class="p-golden-lg space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
            <Users size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Customers</h1>
            <p class="text-white/70">Manage your customer relationships</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          {/* Real-time Status */}
          <div class="flex items-center space-x-golden-xs">
            <Show
              when={!customersQuery.isLoading}
              fallback={
                <div class="flex items-center space-x-golden-xs text-yellow-400">
                  <RefreshCw size={14} class="animate-spin" />
                  <span class="text-xs">Loading...</span>
                </div>
              }
            >
              <div class="flex items-center space-x-golden-xs text-green-400">
                <Activity size={14} />
                <span class="text-xs">{totalCustomers()} customers</span>
              </div>
            </Show>
          </div>

          <GoldenButton
            variant="cosmic"
            size="md"
            glow
            onClick={() => customersQuery.refetch()}
            disabled={customersQuery.isFetching}
          >
            <RefreshCw size={16} class={`mr-golden-xs ${customersQuery.isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </GoldenButton>

          <GoldenButton variant="divine" size="md" glow>
            <Download size={16} class="mr-golden-xs" />
            Export
          </GoldenButton>

          <GoldenButton variant="golden" size="md" glow onClick={() => setShowAddModal(true)}>
            <UserPlus size={16} class="mr-golden-xs" />
            Add Customer
          </GoldenButton>
        </div>
      </div>

      {/* Enhanced Filters and Search */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="md" glow>
          <div class="space-y-golden-md">
            {/* Main Search and Controls */}
            <div class="flex flex-col lg:flex-row lg:items-center space-y-golden-md lg:space-y-0 lg:space-x-golden-md">
              {/* Enhanced Search */}
              <div class="flex-1 relative">
                <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
                <input
                  type="text"
                  placeholder="Search customers by name, email, phone, or tags..."
                  class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400 transition-all duration-300"
                  value={searchTerm()}
                  onInput={(e) => setSearchTerm(e.target.value)}
                />
                <Show when={searchTerm()}>
                  <button
                    class="absolute right-golden-sm top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white transition-colors"
                    onClick={() => setSearchTerm('')}
                  >
                    <XCircle size={16} />
                  </button>
                </Show>
              </div>

              {/* View Mode Toggle */}
              <div class="flex items-center space-x-golden-xs bg-white/5 rounded-lg p-1">
                <button
                  class={`p-2 rounded transition-all duration-300 ${
                    viewMode() === 'list'
                      ? 'bg-cosmic-400 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                  onClick={() => setViewMode('list')}
                  title="List View"
                >
                  <Layers size={16} />
                </button>
                <button
                  class={`p-2 rounded transition-all duration-300 ${
                    viewMode() === 'grid'
                      ? 'bg-cosmic-400 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                  onClick={() => setViewMode('grid')}
                  title="Grid View"
                >
                  <BarChart3 size={16} />
                </button>
              </div>

              {/* Advanced Filters Toggle */}
              <GoldenButton
                variant={showAdvancedFilters() ? 'cosmic' : 'glass'}
                size="sm"
                glow={showAdvancedFilters()}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters())}
              >
                <Filter size={16} class="mr-golden-xs" />
                Advanced
              </GoldenButton>

              {/* Analytics Toggle */}
              <GoldenButton
                variant={showCustomerAnalytics() ? 'cosmic' : 'glass'}
                size="sm"
                glow={showCustomerAnalytics()}
                onClick={() => setShowCustomerAnalytics(!showCustomerAnalytics())}
              >
                <BarChart3 size={16} class="mr-golden-xs" />
                Analytics
              </GoldenButton>

              {/* Bulk Actions */}
              <Show when={selectedCustomers().length > 0}>
                <GoldenButton
                  variant="divine"
                  size="sm"
                  glow
                  onClick={() => setShowBulkActions(!showBulkActions())}
                >
                  <Settings size={16} class="mr-golden-xs" />
                  Bulk ({selectedCustomers().length})
                </GoldenButton>
              </Show>
            </div>

            {/* Advanced Filters Panel */}
            <Show when={showAdvancedFilters()}>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md p-golden-md bg-white/5 rounded-lg border border-white/10">
                {/* Status Filter */}
                <div class="space-y-golden-xs">
                  <label class="text-white/70 text-sm font-medium">Status</label>
                  <select
                    class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-sm py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                    value={selectedStatus()}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="pending">Pending</option>
                    <option value="inactive">Inactive</option>
                    <option value="prospect">Prospect</option>
                  </select>
                </div>

                {/* Location Filter */}
                <div class="space-y-golden-xs">
                  <label class="text-white/70 text-sm font-medium">Location</label>
                  <select
                    class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-sm py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                    value={selectedLocation()}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                  >
                    <option value="all">All Locations</option>
                    <option value="warsaw">Warsaw</option>
                    <option value="krakow">Krakow</option>
                    <option value="gdansk">Gdansk</option>
                    <option value="wroclaw">Wroclaw</option>
                  </select>
                </div>

                {/* Rating Filter */}
                <div class="space-y-golden-xs">
                  <label class="text-white/70 text-sm font-medium">AI Score</label>
                  <select
                    class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-sm py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                    value={selectedRating()}
                    onChange={(e) => setSelectedRating(e.target.value)}
                  >
                    <option value="all">All Scores</option>
                    <option value="high">High (80+)</option>
                    <option value="medium">Medium (60-79)</option>
                    <option value="low">Low (&lt;60)</option>
                  </select>
                </div>

                {/* Quick Actions */}
                <div class="space-y-golden-xs">
                  <label class="text-white/70 text-sm font-medium">Quick Actions</label>
                  <div class="flex space-x-golden-xs">
                    <GoldenButton variant="cosmic" size="xs" glow>
                      <Lightning size={12} class="mr-1" />
                      AI Insights
                    </GoldenButton>
                    <GoldenButton variant="golden" size="xs" glow>
                      <Download size={12} />
                    </GoldenButton>
                  </div>
                </div>
              </div>
            </Show>

            {/* Bulk Actions Panel */}
            <Show when={showBulkActions() && selectedCustomers().length > 0}>
              <div class="flex items-center justify-between p-golden-md bg-divine-500/20 rounded-lg border border-divine-400/30">
                <div class="flex items-center space-x-golden-md">
                  <span class="text-white font-medium">
                    {selectedCustomers().length} customers selected
                  </span>
                  <button
                    class="text-white/70 hover:text-white transition-colors"
                    onClick={() => setSelectedCustomers([])}
                  >
                    Clear selection
                  </button>
                </div>
                <div class="flex items-center space-x-golden-sm">
                  <GoldenButton variant="cosmic" size="sm" glow>
                    <Mail size={14} class="mr-golden-xs" />
                    Email
                  </GoldenButton>
                  <GoldenButton variant="golden" size="sm" glow>
                    <Tag size={14} class="mr-golden-xs" />
                    Tag
                  </GoldenButton>
                  <GoldenButton variant="divine" size="sm" glow>
                    <Download size={14} class="mr-golden-xs" />
                    Export
                  </GoldenButton>
                </div>
              </div>
            </Show>
          </div>
        </CosmicCard>
      </div>

      {/* Enhanced Customer Stats with AI Insights */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {customers().filter(c => c.status === 'active').length}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Active Customers</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <TrendingUp size={12} class="text-green-400" />
                <span class="text-green-400 text-xs">+12.5%</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-cosmic-600 rounded-lg flex items-center justify-center">
              <Users size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="golden" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {customers().filter(c => c.status === 'prospect').length}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Prospects</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Target size={12} class="text-yellow-400" />
                <span class="text-yellow-400 text-xs">Hot leads</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-golden-400 to-golden-600 rounded-lg flex items-center justify-center">
              <Target size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="divine" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {Math.round(customers().reduce((sum, c) => sum + getCustomerScore(c), 0) / Math.max(customers().length, 1))}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Avg AI Score</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Brain size={12} class="text-purple-400" />
                <span class="text-purple-400 text-xs">AI Powered</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-divine-600 rounded-lg flex items-center justify-center">
              <Brain size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="glass" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {totalPages()}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Total Pages</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Calendar size={12} class="text-blue-400" />
                <span class="text-blue-400 text-xs">Page {currentPage()}</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-600 rounded-lg flex items-center justify-center">
              <Activity size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Enhanced Customer List with AI Insights */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">Customer Directory</h3>
            <div class="flex items-center space-x-golden-sm">
              <span class="text-white/70 text-sm">
                Showing {customers().length} of {totalCustomers()} customers
              </span>
              <select
                class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-sm py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={pageSize()}
                onChange={(e) => setPageSize(Number(e.target.value))}
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>

          <Show
            when={!customersQuery.isLoading}
            fallback={
              <div class="space-y-golden-md">
                {Array.from({ length: 5 }, (_, index) => (
                  <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 animate-pulse">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="h-6 bg-white/20 rounded mb-golden-xs w-1/3"></div>
                        <div class="h-4 bg-white/20 rounded mb-golden-sm w-1/4"></div>
                        <div class="grid grid-cols-3 gap-golden-sm">
                          <div class="h-4 bg-white/20 rounded"></div>
                          <div class="h-4 bg-white/20 rounded"></div>
                          <div class="h-4 bg-white/20 rounded"></div>
                        </div>
                      </div>
                      <div class="flex space-x-golden-xs">
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            }
          >
            <div class={`${viewMode() === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-golden-md' : 'space-y-golden-md'}`}>
              <For each={customers()}>
                {(customer) => {
                  const aiScore = getCustomerScore(customer)
                  const avatar = getCustomerAvatar(customer)
                  const StatusIcon = getStatusIcon(customer.status)
                  const insights = getCustomerInsights(customer)
                  const isSelected = selectedCustomers().includes(customer.id)

                  return (
                    <div class={`bg-white/5 rounded-xl p-golden-md border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10 group hover:scale-[1.02] hover:shadow-2xl ${isSelected ? 'ring-2 ring-cosmic-400 bg-cosmic-500/10' : ''}`}>
                      {/* Selection Checkbox */}
                      <div class="flex items-start justify-between mb-golden-md">
                        <div class="flex items-center space-x-golden-md flex-1">
                          <button
                            class={`w-5 h-5 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                              isSelected
                                ? 'bg-cosmic-400 border-cosmic-400'
                                : 'border-white/30 hover:border-cosmic-400'
                            }`}
                            onClick={() => toggleCustomerSelection(customer.id)}
                          >
                            <Show when={isSelected}>
                              <CheckCircle size={12} class="text-white" />
                            </Show>
                          </button>

                          {/* Enhanced Avatar */}
                          <div class={`w-12 h-12 bg-gradient-to-r ${avatar.gradient} rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 group-hover:ring-white/40 transition-all duration-300`}>
                            <span class="text-white text-sm font-bold">{avatar.initials}</span>
                          </div>

                          {/* Customer Info */}
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-golden-sm mb-1">
                              <h3 class="text-lg font-bold text-white truncate">{customer.name}</h3>
                              <StatusIcon size={14} class={getStatusColor(customer.status)} />
                            </div>
                            <div class="flex items-center space-x-golden-sm">
                              <span class="text-white/70 text-sm">{getStatusText(customer.status)}</span>
                              <div class="w-1 h-1 bg-white/30 rounded-full"></div>
                              <span class="text-white/50 text-xs">ID: {customer.id}</span>
                            </div>
                          </div>
                        </div>

                        {/* AI Score Badge */}
                        <div class="flex items-center space-x-golden-xs">
                          <div class={`px-2 py-1 rounded-full bg-gradient-to-r ${
                            aiScore >= 80 ? 'from-green-400/20 to-emerald-400/20 border border-green-400/30' :
                            aiScore >= 60 ? 'from-yellow-400/20 to-orange-400/20 border border-yellow-400/30' :
                            'from-red-400/20 to-pink-400/20 border border-red-400/30'
                          }`}>
                            <div class="flex items-center space-x-1">
                              <Brain size={10} class={getScoreColor(aiScore)} />
                              <span class={`text-xs font-bold ${getScoreColor(aiScore)}`}>
                                {aiScore}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Contact Information */}
                      <div class="space-y-golden-sm mb-golden-md">
                        <div class="flex items-center space-x-golden-xs text-white/70 hover:text-white transition-colors group/contact">
                          <Mail size={14} class="text-cosmic-400" />
                          <span class="text-sm truncate flex-1">{customer.email}</span>
                          <button class="opacity-0 group-hover/contact:opacity-100 transition-opacity">
                            <Copy size={12} class="text-white/50 hover:text-white" />
                          </button>
                        </div>

                        <Show when={customer.phone}>
                          <div class="flex items-center space-x-golden-xs text-white/70 hover:text-white transition-colors group/contact">
                            <Phone size={14} class="text-golden-400" />
                            <span class="text-sm">{customer.phone}</span>
                            <button class="opacity-0 group-hover/contact:opacity-100 transition-opacity">
                              <ExternalLink size={12} class="text-white/50 hover:text-white" />
                            </button>
                          </div>
                        </Show>

                        <Show when={customer.address}>
                          <div class="flex items-center space-x-golden-xs text-white/70">
                            <MapPin size={14} class="text-divine-400" />
                            <span class="text-sm truncate">{customer.address}</span>
                          </div>
                        </Show>
                      </div>

                      {/* AI Insights */}
                      <Show when={insights.length > 0}>
                        <div class="mb-golden-md">
                          <div class="flex flex-wrap gap-1">
                            <For each={insights.slice(0, 2)}>
                              {(insight) => {
                                const InsightIcon = insight.icon
                                return (
                                  <div class={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                                    insight.type === 'success' ? 'bg-green-400/20 text-green-300' :
                                    insight.type === 'warning' ? 'bg-yellow-400/20 text-yellow-300' :
                                    'bg-blue-400/20 text-blue-300'
                                  }`}>
                                    <InsightIcon size={10} />
                                    <span>{insight.text}</span>
                                  </div>
                                )
                              }}
                            </For>
                          </div>
                        </div>
                      </Show>

                      {/* Tags */}
                      <Show when={customer.tags && customer.tags.length > 0}>
                        <div class="mb-golden-md">
                          <div class="flex flex-wrap gap-1">
                            <For each={customer.tags?.slice(0, 3)}>
                              {(tag) => (
                                <span class="px-2 py-0.5 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full border border-cosmic-400/30">
                                  {tag}
                                </span>
                              )}
                            </For>
                            <Show when={(customer.tags?.length || 0) > 3}>
                              <span class="text-white/50 text-xs px-2 py-0.5">+{(customer.tags?.length || 0) - 3}</span>
                            </Show>
                          </div>
                        </div>
                      </Show>

                      {/* Enhanced Actions */}
                      <div class="flex items-center justify-between pt-golden-sm border-t border-white/10">
                        <div class="flex items-center space-x-golden-xs">
                          <GoldenButton
                            variant="cosmic"
                            size="xs"
                            glow
                            title="Quick Call"
                          >
                            <Phone size={12} />
                          </GoldenButton>
                          <GoldenButton
                            variant="golden"
                            size="xs"
                            glow
                            title="Send Email"
                          >
                            <Mail size={12} />
                          </GoldenButton>
                          <GoldenButton
                            variant="divine"
                            size="xs"
                            glow
                            title="Video Call"
                          >
                            <Video size={12} />
                          </GoldenButton>
                        </div>

                        <div class="flex items-center space-x-golden-xs">
                          <GoldenButton
                            variant="glass"
                            size="xs"
                            glow
                            title="View Details"
                            onClick={() => handleViewCustomer(customer.id)}
                          >
                            <Eye size={12} />
                          </GoldenButton>
                          <GoldenButton
                            variant="glass"
                            size="xs"
                            glow
                            title="Edit Customer"
                          >
                            <Edit size={12} />
                          </GoldenButton>
                          <GoldenButton
                            variant="glass"
                            size="xs"
                            glow
                            onClick={() => handleDeleteCustomer(customer.id)}
                            disabled={deleteCustomerMutation.isPending}
                            title="Delete Customer"
                          >
                            <Trash2 size={12} />
                          </GoldenButton>
                        </div>
                      </div>
                    </div>
                  )
                }}
              </For>
            </div>
          </Show>

          {/* Enhanced Pagination and Controls */}
          <div class="mt-golden-lg pt-golden-md border-t border-white/10">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-golden-md lg:space-y-0">
              {/* Selection Controls */}
              <div class="flex items-center space-x-golden-md">
                <button
                  class="flex items-center space-x-golden-xs text-white/70 hover:text-white transition-colors"
                  onClick={selectAllCustomers}
                >
                  <div class={`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                    selectedCustomers().length === customers().length && customers().length > 0
                      ? 'bg-cosmic-400 border-cosmic-400'
                      : selectedCustomers().length > 0
                      ? 'bg-cosmic-400/50 border-cosmic-400'
                      : 'border-white/30 hover:border-cosmic-400'
                  }`}>
                    <Show when={selectedCustomers().length === customers().length && customers().length > 0}>
                      <CheckCircle size={10} class="text-white" />
                    </Show>
                    <Show when={selectedCustomers().length > 0 && selectedCustomers().length < customers().length}>
                      <div class="w-2 h-2 bg-white rounded-full"></div>
                    </Show>
                  </div>
                  <span class="text-sm">
                    {selectedCustomers().length === customers().length && customers().length > 0
                      ? 'Deselect All'
                      : 'Select All'
                    }
                  </span>
                </button>

                <Show when={selectedCustomers().length > 0}>
                  <span class="text-cosmic-400 text-sm font-medium">
                    {selectedCustomers().length} selected
                  </span>
                </Show>
              </div>

              {/* Pagination Info and Controls */}
              <div class="flex items-center space-x-golden-md">
                <div class="text-white/70 text-sm">
                  Showing {customers().length} of {totalCustomers()} customers
                  <Show when={totalPages() > 1}>
                    <span class="ml-2">• Page {currentPage()} of {totalPages()}</span>
                  </Show>
                </div>

                <Show when={totalPages() > 1}>
                  <div class="flex items-center space-x-golden-sm">
                    <GoldenButton
                      variant="glass"
                      size="sm"
                      glow
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage() === 1}
                      title="First Page"
                    >
                      <span class="text-xs">First</span>
                    </GoldenButton>

                    <GoldenButton
                      variant="cosmic"
                      size="sm"
                      glow
                      onClick={() => setCurrentPage(Math.max(1, currentPage() - 1))}
                      disabled={currentPage() === 1}
                    >
                      Previous
                    </GoldenButton>

                    {/* Page Numbers */}
                    <div class="flex items-center space-x-1">
                      <For each={Array.from({ length: Math.min(5, totalPages()) }, (_, i) => {
                        const start = Math.max(1, currentPage() - 2)
                        const end = Math.min(totalPages(), start + 4)
                        return start + i <= end ? start + i : null
                      }).filter(Boolean)}>
                        {(page) => (
                          <button
                            class={`w-8 h-8 rounded text-xs font-medium transition-all duration-300 ${
                              page === currentPage()
                                ? 'bg-cosmic-400 text-white'
                                : 'text-white/70 hover:text-white hover:bg-white/10'
                            }`}
                            onClick={() => setCurrentPage(page!)}
                          >
                            {page}
                          </button>
                        )}
                      </For>
                    </div>

                    <GoldenButton
                      variant="cosmic"
                      size="sm"
                      glow
                      onClick={() => setCurrentPage(Math.min(totalPages(), currentPage() + 1))}
                      disabled={currentPage() === totalPages()}
                    >
                      Next
                    </GoldenButton>

                    <GoldenButton
                      variant="glass"
                      size="sm"
                      glow
                      onClick={() => setCurrentPage(totalPages())}
                      disabled={currentPage() === totalPages()}
                      title="Last Page"
                    >
                      <span class="text-xs">Last</span>
                    </GoldenButton>
                  </div>
                </Show>
              </div>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Customer Analytics Dashboard */}
      <Show when={showCustomerAnalytics()}>
        <div
          class={`transition-all duration-1000 delay-800 ${
            isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}
        >
          <CustomerAnalyticsDashboard />
        </div>
      </Show>

      {/* Customer Details Modal */}
      <CustomerDetailsModal
        customerId={selectedCustomerId()}
        isOpen={showDetailsModal()}
        onClose={handleCloseDetailsModal}
      />
    </div>
  )
}
