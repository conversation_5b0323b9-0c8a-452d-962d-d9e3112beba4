import { type Component, type JSX, createSignal, onMount } from 'solid-js'
import { A, useLocation } from '@solidjs/router'
import {
  Home,
  BarChart3,
  Users,
  Wrench,
  Package,
  TrendingUp,
  Settings,
  Menu,
  X,
  Zap,
  Cog,
  Search,
  Bell,
  User,
  ChevronDown,
  Command
} from 'lucide-solid'

interface MainLayoutProps {
  children: JSX.Element
}

export const MainLayout: Component<MainLayoutProps> = (props) => {
  const [isSidebarOpen, setIsSidebarOpen] = createSignal(false)
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [isSearchOpen, setIsSearchOpen] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [isUserMenuOpen, setIsUserMenuOpen] = createSignal(false)
  const location = useLocation()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 100)
  })

  const navigation = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
    { name: 'Enhanced Dashboard', href: '/dashboard-enhanced', icon: Zap },
    { name: 'Equipment', href: '/equipment', icon: Cog },
    { name: 'Customers', href: '/customers', icon: Users },
    { name: 'Service Orders', href: '/service-orders', icon: Wrench },
    { name: 'Inventory', href: '/inventory', icon: Package },
    { name: 'Analytics', href: '/analytics', icon: TrendingUp },
    { name: 'Settings', href: '/settings', icon: Settings },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(href)
  }

  return (
    <div class="min-h-screen bg-gradient-to-br from-cosmic-500 via-divine-500 to-golden-500 relative">
      {/* Cosmic Background Effects */}
      <div class="absolute inset-0 pointer-events-none">
        {/* Floating Orbs */}
        {Array.from({ length: 15 }, (_, index) => (
          <div
            class="absolute rounded-full bg-white/5 backdrop-blur-sm animate-pulse"
            style={{
              width: `${Math.random() * 80 + 20}px`,
              height: `${Math.random() * 80 + 20}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              'animation-delay': `${index * 0.3}s`,
              'animation-duration': `${4 + Math.random() * 3}s`
            }}
          />
        ))}

        {/* Golden Ratio Spiral */}
        <div class="absolute top-1/4 right-1/4 transform opacity-5">
          <svg width="300" height="300" viewBox="0 0 300 300">
            <path
              d="M150,150 Q225,150 225,75 Q225,0 150,0 Q75,0 75,75 Q75,150 150,150"
              fill="none"
              stroke="white"
              stroke-width="1"
              class="animate-pulse"
            />
          </svg>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      <div
        class={`fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300 ${
          isSidebarOpen() ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setIsSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div
        class={`fixed inset-y-0 left-0 z-50 w-64 bg-white/10 backdrop-blur-lg border-r border-white/20 transform transition-transform duration-300 ease-out ${
          isSidebarOpen() ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        }`}
      >
        <div class="flex flex-col h-full">
          {/* Logo */}
          <div class="flex items-center justify-between p-golden-md border-b border-white/20">
            <div class="flex items-center space-x-golden-sm">
              <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">H</span>
              </div>
              <span class="text-white font-bold text-lg">HVAC CRM</span>
            </div>
            <button
              class="lg:hidden text-white/60 hover:text-white transition-colors"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X size={20} />
            </button>
          </div>

          {/* Enhanced Navigation with Cosmic Animations */}
          <nav class="flex-1 p-golden-sm space-y-golden-xs">
            {navigation.map((item, index) => {
              const Icon = item.icon
              const active = isActive(item.href)
              return (
                <A
                  href={item.href}
                  class={`group relative flex items-center space-x-golden-sm px-golden-sm py-golden-sm rounded-lg transition-all duration-300 ease-out transform hover:scale-105 ${
                    active
                      ? 'bg-gradient-to-r from-cosmic-500/30 to-divine-500/30 text-white shadow-lg shadow-cosmic-500/20 border border-cosmic-400/30'
                      : 'text-white/70 hover:text-white hover:bg-white/10 hover:shadow-md'
                  }`}
                  style={{
                    'animation-delay': `${index * 50}ms`
                  }}
                  onClick={() => setIsSidebarOpen(false)}
                >
                  {/* Active Indicator */}
                  <div class={`absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-cosmic-400 to-divine-400 rounded-r-full transition-all duration-300 ${
                    active ? 'opacity-100 scale-100' : 'opacity-0 scale-0'
                  }`} />

                  {/* Icon with Enhanced Animation */}
                  <div class={`relative transition-all duration-300 ${
                    active ? 'text-cosmic-300' : 'text-white/70 group-hover:text-white'
                  }`}>
                    <Icon
                      size={20}
                      class={`transition-all duration-300 ${
                        active ? 'drop-shadow-lg' : 'group-hover:scale-110'
                      }`}
                    />

                    {/* Cosmic Glow Effect */}
                    <div class={`absolute inset-0 transition-all duration-300 ${
                      active
                        ? 'bg-cosmic-400/20 blur-md scale-150 opacity-100'
                        : 'bg-cosmic-400/10 blur-md scale-100 opacity-0 group-hover:opacity-50'
                    }`} />
                  </div>

                  {/* Text with Enhanced Typography */}
                  <span class={`font-medium transition-all duration-300 ${
                    active
                      ? 'text-white font-semibold tracking-wide'
                      : 'text-white/70 group-hover:text-white group-hover:font-semibold'
                  }`}>
                    {item.name}
                  </span>

                  {/* Hover Ripple Effect */}
                  <div class="absolute inset-0 rounded-lg overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-cosmic-400/0 via-cosmic-400/10 to-cosmic-400/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out" />
                  </div>

                  {/* Active Pulse Animation */}
                  {active && (
                    <div class="absolute inset-0 rounded-lg bg-cosmic-400/5 animate-pulse" />
                  )}
                </A>
              )
            })}
          </nav>

          {/* Footer */}
          <div class="p-golden-md border-t border-white/20">
            <div class="text-white/60 text-sm text-center">
              <p>Cosmic CRM v1.0</p>
              <p class="text-xs mt-1">Powered by 137 truths</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div class="lg:pl-64">
        {/* Enhanced Top Bar */}
        <header class="bg-white/10 backdrop-blur-lg border-b border-white/20 sticky top-0 z-30">
          <div class="flex items-center justify-between px-golden-md py-golden-sm">
            {/* Left Section */}
            <div class="flex items-center space-x-golden-md">
              <button
                class="lg:hidden text-white/60 hover:text-white transition-all duration-200 hover:scale-110"
                onClick={() => setIsSidebarOpen(true)}
              >
                <Menu size={24} />
              </button>

              <h1 class="text-white font-semibold text-lg bg-gradient-to-r from-cosmic-300 to-divine-300 bg-clip-text text-transparent">
                {navigation.find(nav => isActive(nav.href))?.name || 'HVAC CRM'}
              </h1>
            </div>

            {/* Center Section - Enhanced Search */}
            <div class="hidden md:flex flex-1 max-w-md mx-golden-lg">
              <div class="relative w-full group">
                <div class={`absolute inset-0 bg-gradient-to-r from-cosmic-500/20 to-divine-500/20 rounded-lg blur-sm transition-all duration-300 ${
                  isSearchOpen() ? 'opacity-100 scale-105' : 'opacity-0 scale-100'
                }`} />

                <div class="relative flex items-center">
                  <Search size={18} class="absolute left-golden-sm text-white/50 transition-colors duration-200 group-focus-within:text-cosmic-400" />
                  <input
                    type="text"
                    placeholder="Search equipment, customers, orders... (⌘K)"
                    value={searchTerm()}
                    onInput={(e) => setSearchTerm(e.currentTarget.value)}
                    onFocus={() => setIsSearchOpen(true)}
                    onBlur={() => setIsSearchOpen(false)}
                    class="w-full bg-white/5 border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:border-cosmic-400 focus:bg-white/10 transition-all duration-300"
                  />
                  <div class="absolute right-golden-sm flex items-center space-x-golden-xs">
                    <kbd class="hidden sm:inline-flex items-center px-golden-xs py-0.5 border border-white/20 rounded text-xs text-white/50">
                      <Command size={10} class="mr-0.5" />
                      K
                    </kbd>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section - Enhanced User Menu */}
            <div class="flex items-center space-x-golden-sm">
              {/* Mobile Search Button */}
              <button class="md:hidden text-white/60 hover:text-white transition-all duration-200 hover:scale-110">
                <Search size={20} />
              </button>

              {/* Notifications */}
              <div class="relative">
                <button class="text-white/60 hover:text-white transition-all duration-200 hover:scale-110 relative">
                  <Bell size={20} />
                  <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                    <span class="text-xs text-white font-bold">3</span>
                  </div>
                </button>
              </div>

              {/* User Menu */}
              <div class="relative">
                <button
                  class="flex items-center space-x-golden-xs text-white/60 hover:text-white transition-all duration-200 hover:scale-105"
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen())}
                >
                  <div class="w-8 h-8 bg-gradient-to-r from-golden-400 to-cosmic-400 rounded-full flex items-center justify-center shadow-lg">
                    <User size={16} class="text-white" />
                  </div>
                  <ChevronDown
                    size={16}
                    class={`transition-transform duration-200 ${isUserMenuOpen() ? 'rotate-180' : ''}`}
                  />
                </button>

                {/* User Dropdown Menu */}
                <div class={`absolute right-0 top-full mt-golden-xs w-48 bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg shadow-xl transition-all duration-200 ${
                  isUserMenuOpen()
                    ? 'opacity-100 scale-100 translate-y-0'
                    : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
                }`}>
                  <div class="p-golden-sm">
                    <div class="px-golden-sm py-golden-xs border-b border-white/20 mb-golden-xs">
                      <p class="text-white font-medium text-sm">John Doe</p>
                      <p class="text-white/60 text-xs"><EMAIL></p>
                    </div>

                    <div class="space-y-golden-xs">
                      <button class="w-full text-left px-golden-sm py-golden-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200 text-sm">
                        Profile Settings
                      </button>
                      <button class="w-full text-left px-golden-sm py-golden-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200 text-sm">
                        Preferences
                      </button>
                      <button class="w-full text-left px-golden-sm py-golden-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200 text-sm">
                        Help & Support
                      </button>
                      <hr class="border-white/20" />
                      <button class="w-full text-left px-golden-sm py-golden-xs text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded transition-all duration-200 text-sm">
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main
          class={`relative z-10 transition-all duration-1000 ${
            isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
          }`}
        >
          {props.children}
        </main>
      </div>
    </div>
  )
}
