// 🔧 COSMIC EQUIPMENT CARD - Advanced Equipment Display
// Beautiful equipment cards with AI insights and cosmic design

import { type Component, Show, createMemo } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import type { EquipmentSummary, EquipmentCategory, EquipmentStatus } from '../../types/equipment'
import {
  <PERSON>ch,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Settings,
  QrCode,
  Eye,
  Calendar,
  Shield,
  TrendingUp,
  TrendingDown,
  Activity,
  Thermometer,
  Gauge,
  Brain,
  Star,
  MapPin,
  Package,
  Wifi,
  WifiOff
} from 'lucide-solid'

interface EquipmentCardProps {
  equipment: EquipmentSummary
  onView?: (id: string) => void
  onEdit?: (id: string) => void
  onMaintenance?: (id: string) => void
  onGenerateQR?: (id: string) => void
  variant?: 'default' | 'compact' | 'detailed'
  showActions?: boolean
  cosmicMode?: boolean
}

export const EquipmentCard: Component<EquipmentCardProps> = (props) => {
  // 🎯 Equipment Category Icons
  const getCategoryIcon = (category: EquipmentCategory) => {
    switch (category) {
      case 'HVAC_UNIT': return Thermometer
      case 'AIR_HANDLER': return Activity
      case 'CONDENSER': return Gauge
      case 'EVAPORATOR': return Thermometer
      case 'FURNACE': return Zap
      case 'HEAT_PUMP': return TrendingUp
      case 'BOILER': return Thermometer
      case 'CHILLER': return TrendingDown
      case 'THERMOSTAT': return Settings
      case 'DUCTWORK': return Activity
      case 'VENTILATION': return Activity
      case 'FILTER_SYSTEM': return Package
      case 'CONTROL_SYSTEM': return Settings
      default: return Wrench
    }
  }

  // 🎨 Status Colors & Icons
  const getStatusConfig = (status: EquipmentStatus) => {
    switch (status) {
      case 'ACTIVE':
        return { 
          icon: CheckCircle, 
          color: 'text-green-400', 
          bgColor: 'bg-green-400/10',
          label: 'Active'
        }
      case 'INACTIVE':
        return { 
          icon: WifiOff, 
          color: 'text-gray-400', 
          bgColor: 'bg-gray-400/10',
          label: 'Inactive'
        }
      case 'MAINTENANCE':
        return { 
          icon: Wrench, 
          color: 'text-yellow-400', 
          bgColor: 'bg-yellow-400/10',
          label: 'Maintenance'
        }
      case 'NEEDS_REPAIR':
        return { 
          icon: AlertTriangle, 
          color: 'text-red-400', 
          bgColor: 'bg-red-400/10',
          label: 'Needs Repair'
        }
      case 'NEEDS_REPLACEMENT':
        return { 
          icon: AlertTriangle, 
          color: 'text-red-500', 
          bgColor: 'bg-red-500/10',
          label: 'Replace Soon'
        }
      case 'DECOMMISSIONED':
        return { 
          icon: WifiOff, 
          color: 'text-gray-500', 
          bgColor: 'bg-gray-500/10',
          label: 'Decommissioned'
        }
      default:
        return { 
          icon: Wifi, 
          color: 'text-cosmic-400', 
          bgColor: 'bg-cosmic-400/10',
          label: 'Unknown'
        }
    }
  }

  // 🧠 Health Score Analysis
  const healthAnalysis = createMemo(() => {
    const score = props.equipment.healthScore
    if (score >= 90) return { 
      level: 'Excellent', 
      color: 'text-green-400', 
      bgColor: 'bg-green-400/10',
      icon: Star
    }
    if (score >= 75) return { 
      level: 'Good', 
      color: 'text-cosmic-400', 
      bgColor: 'bg-cosmic-400/10',
      icon: CheckCircle
    }
    if (score >= 60) return { 
      level: 'Fair', 
      color: 'text-yellow-400', 
      bgColor: 'bg-yellow-400/10',
      icon: Clock
    }
    return { 
      level: 'Poor', 
      color: 'text-red-400', 
      bgColor: 'bg-red-400/10',
      icon: AlertTriangle
    }
  })

  // 🚨 Maintenance Status
  const maintenanceStatus = createMemo(() => {
    const nextMaintenance = props.equipment.nextMaintenance
    if (!nextMaintenance) return null
    
    const now = new Date()
    const maintenanceDate = new Date(nextMaintenance)
    const daysUntil = Math.ceil((maintenanceDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntil < 0) return { 
      status: 'Overdue', 
      color: 'text-red-400', 
      bgColor: 'bg-red-400/10',
      urgent: true
    }
    if (daysUntil <= 7) return { 
      status: `Due in ${daysUntil} days`, 
      color: 'text-yellow-400', 
      bgColor: 'bg-yellow-400/10',
      urgent: true
    }
    if (daysUntil <= 30) return { 
      status: `Due in ${daysUntil} days`, 
      color: 'text-cosmic-400', 
      bgColor: 'bg-cosmic-400/10',
      urgent: false
    }
    return { 
      status: `Due in ${daysUntil} days`, 
      color: 'text-green-400', 
      bgColor: 'bg-green-400/10',
      urgent: false
    }
  })

  const CategoryIcon = getCategoryIcon(props.equipment.category)
  const statusConfig = getStatusConfig(props.equipment.status)
  const StatusIcon = statusConfig.icon
  const HealthIcon = healthAnalysis().icon

  return (
    <CosmicCard
      variant={props.cosmicMode ? "cosmic" : "glass"}
      size={props.variant === 'compact' ? 'md' : 'lg'}
      glow
      hover3d
      springMotion={true}
      entranceAnimation={true}
      class="relative overflow-hidden"
    >
      {/* 🚨 Alert Badge */}
      <Show when={props.equipment.alertCount > 0}>
        <div class="absolute top-golden-sm right-golden-sm">
          <div class="bg-red-500 text-white text-xs font-bold px-golden-xs py-golden-xs rounded-full min-w-[20px] h-5 flex items-center justify-center">
            {props.equipment.alertCount}
          </div>
        </div>
      </Show>

      <div class="space-y-golden-md">
        {/* 🏷️ Header Section */}
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-golden-sm flex-1">
            {/* Category Icon */}
            <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-cosmic-600 rounded-lg flex items-center justify-center shadow-lg">
              <CategoryIcon size={24} class="text-white" />
            </div>
            
            <div class="flex-1 min-w-0">
              <h3 class="text-lg font-bold text-white truncate">{props.equipment.name}</h3>
              <p class="text-white/70 text-sm">{props.equipment.manufacturer} {props.equipment.model}</p>
              
              {/* Location */}
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <MapPin size={12} class="text-white/50" />
                <span class="text-white/50 text-xs">{props.equipment.location}</span>
              </div>
            </div>
          </div>

          {/* Status Badge */}
          <div class={`flex items-center space-x-golden-xs px-golden-sm py-golden-xs rounded-lg ${statusConfig.bgColor}`}>
            <StatusIcon size={14} class={statusConfig.color} />
            <span class={`text-xs font-medium ${statusConfig.color}`}>
              {statusConfig.label}
            </span>
          </div>
        </div>

        {/* 📊 Metrics Grid */}
        <div class="grid grid-cols-2 gap-golden-sm">
          {/* Health Score */}
          <div class={`p-golden-sm rounded-lg ${healthAnalysis().bgColor} border border-white/10`}>
            <div class="flex items-center space-x-golden-xs mb-golden-xs">
              <HealthIcon size={14} class={healthAnalysis().color} />
              <span class="text-xs text-white/70">Health Score</span>
            </div>
            <div class="flex items-baseline space-x-golden-xs">
              <span class={`text-lg font-bold ${healthAnalysis().color}`}>
                {props.equipment.healthScore}%
              </span>
              <span class={`text-xs ${healthAnalysis().color}`}>
                {healthAnalysis().level}
              </span>
            </div>
          </div>

          {/* Warranty Status */}
          <div class="p-golden-sm rounded-lg bg-white/5 border border-white/10">
            <div class="flex items-center space-x-golden-xs mb-golden-xs">
              <Shield size={14} class="text-divine-400" />
              <span class="text-xs text-white/70">Warranty</span>
            </div>
            <div class="text-xs text-white/70">
              Expires: {new Date(props.equipment.warrantyExpiry).toLocaleDateString()}
            </div>
          </div>
        </div>

        {/* 🚨 Maintenance Alert */}
        <Show when={maintenanceStatus()}>
          {(maintenance) => (
            <div class={`p-golden-sm rounded-lg ${maintenance().bgColor} border border-white/10`}>
              <div class="flex items-center space-x-golden-xs">
                <Calendar size={14} class={maintenance().color} />
                <span class={`text-xs font-medium ${maintenance().color}`}>
                  Maintenance {maintenance().status}
                </span>
                <Show when={maintenance().urgent}>
                  <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                </Show>
              </div>
            </div>
          )}
        </Show>

        {/* 🎯 Actions */}
        <Show when={props.showActions !== false}>
          <div class="flex items-center space-x-golden-sm pt-golden-sm border-t border-white/10">
            <GoldenButton
              variant="cosmic"
              size="sm"
              glow
              springMotion={true}
              onClick={() => props.onView?.(props.equipment.id)}
              class="flex-1"
            >
              <Eye size={14} class="mr-golden-xs" />
              View
            </GoldenButton>
            
            <GoldenButton
              variant="golden"
              size="sm"
              glow
              springMotion={true}
              onClick={() => props.onMaintenance?.(props.equipment.id)}
            >
              <Wrench size={14} class="mr-golden-xs" />
              Service
            </GoldenButton>
            
            <GoldenButton
              variant="divine"
              size="sm"
              glow
              springMotion={true}
              onClick={() => props.onGenerateQR?.(props.equipment.id)}
            >
              <QrCode size={14} />
            </GoldenButton>
          </div>
        </Show>

        {/* 🧠 AI Insight Footer */}
        <div class="bg-divine-500/10 rounded-lg p-golden-xs border border-divine-400/20">
          <div class="flex items-center space-x-golden-xs">
            <Brain size={12} class="text-divine-400" />
            <span class="text-divine-400 text-xs font-medium">AI Insight</span>
          </div>
          <p class="text-white/70 text-xs mt-golden-xs">
            {props.equipment.healthScore >= 90 
              ? "Equipment performing optimally. Continue current maintenance schedule."
              : props.equipment.healthScore >= 75
              ? "Good performance. Monitor efficiency trends."
              : "Consider scheduling preventive maintenance soon."
            }
          </p>
        </div>
      </div>
    </CosmicCard>
  )
}
