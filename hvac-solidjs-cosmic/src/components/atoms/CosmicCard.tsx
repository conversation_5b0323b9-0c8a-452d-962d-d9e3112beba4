import { type Component, type JSX, splitProps } from 'solid-js'
import { useCosmicCardSpring } from '../../lib/motion/cosmicSprings'

export interface CosmicCardProps extends JSX.HTMLAttributes<HTMLDivElement> {
  variant?: 'glass' | 'cosmic' | 'golden' | 'divine'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  glow?: boolean
  physics?: boolean
  hover3d?: boolean
  springMotion?: boolean // Enable M3 Expressive spring motion
  entranceAnimation?: boolean // Enable entrance animation
  children: JSX.Element
}

export const CosmicCard: Component<CosmicCardProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant',
    'size',
    'glow',
    'physics',
    'hover3d',
    'springMotion',
    'entranceAnimation',
    'children',
    'class'
  ])

  // 🌟 M3 Expressive Spring Motion
  const springs = local.springMotion ? useCosmicCardSpring() : null
  let cardRef: HTMLDivElement | undefined

  // 🎭 Entrance Animation
  if (local.entranceAnimation && springs) {
    // Set initial state for entrance
    springs.set({
      y: 50,
      scale: 0.95,
      opacity: 0,
      rotateX: 15
    })

    // Animate to final state
    setTimeout(() => {
      springs.set({
        y: 0,
        scale: 1,
        opacity: 1,
        rotateX: 0
      })
    }, 100)
  }

  const getVariantClasses = () => {
    switch (local.variant) {
      case 'glass':
        return 'bg-white/10 backdrop-blur-lg border border-white/20'
      case 'cosmic':
        return 'bg-gradient-to-br from-cosmic-500/20 to-cosmic-600/20 backdrop-blur-lg border border-cosmic-300/30'
      case 'golden':
        return 'bg-gradient-to-br from-golden-500/20 to-golden-600/20 backdrop-blur-lg border border-golden-300/30'
      case 'divine':
        return 'bg-gradient-to-br from-divine-500/20 to-divine-600/20 backdrop-blur-lg border border-divine-300/30'
      default:
        return 'bg-white/10 backdrop-blur-lg border border-white/20'
    }
  }

  const getSizeClasses = () => {
    switch (local.size) {
      case 'sm':
        return 'p-golden-sm rounded-lg'
      case 'md':
        return 'p-golden-md rounded-xl'
      case 'lg':
        return 'p-golden-lg rounded-2xl'
      case 'xl':
        return 'p-golden-xl rounded-3xl'
      default:
        return 'p-golden-md rounded-xl'
    }
  }

  const getGlowClasses = () => {
    if (!local.glow) return ''
    
    switch (local.variant) {
      case 'cosmic':
        return 'hover:shadow-[0_0_30px_rgba(14,165,233,0.3)]'
      case 'golden':
        return 'hover:shadow-[0_0_30px_rgba(245,158,11,0.3)]'
      case 'divine':
        return 'hover:shadow-[0_0_30px_rgba(217,70,239,0.3)]'
      default:
        return 'hover:shadow-[0_0_30px_rgba(255,255,255,0.2)]'
    }
  }

  const baseClasses = `
    relative overflow-hidden
    ${local.springMotion ? '' : 'transition-all duration-500 ease-out'}
    hover:border-white/40
    ${local.physics && !local.springMotion ? 'hover:scale-105 hover:rotate-1' : ''}
    ${local.hover3d ? 'transform-gpu perspective-1000' : ''}
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${getGlowClasses()}
    ${local.class || ''}
  `.trim().replace(/\s+/g, ' ')

  // 🎯 Spring Motion Event Handlers
  const handleMouseEnter = () => {
    if (springs) {
      springs.set({
        scale: 1.02,
        rotateX: -2,
        rotateY: 2
      })
    }
    if (others.onMouseEnter) others.onMouseEnter()
  }

  const handleMouseLeave = () => {
    if (springs) {
      springs.set({
        scale: 1,
        rotateX: 0,
        rotateY: 0
      })
    }
    if (others.onMouseLeave) others.onMouseLeave()
  }

  // 🌟 Dynamic Style with Spring Values
  const dynamicStyle = () => {
    const baseStyle = {
      'transform-style': local.hover3d ? 'preserve-3d' : 'flat',
      perspective: local.hover3d ? '1000px' : 'none'
    }

    if (!springs) return baseStyle

    const springValues = springs.get()
    return {
      ...baseStyle,
      transform: `
        translateY(${springValues.y}px)
        scale(${springValues.scale})
        rotateX(${springValues.rotateX}deg)
        rotateY(${springValues.rotateY}deg)
      `.trim(),
      opacity: springValues.opacity
    }
  }

  return (
    <div
      ref={cardRef}
      class={baseClasses}
      style={dynamicStyle()}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...others}
    >
      {/* Golden Ratio Background Pattern */}
      <div class="absolute inset-0 opacity-5">
        <div class="absolute top-0 left-0 w-full h-full">
          <div class="grid grid-cols-8 grid-rows-5 h-full">
            {Array.from({ length: 40 }, (_, index) => (
              <div
                class="border border-white/10"
                style={{
                  'animation-delay': `${index * 0.1}s`
                }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div class="relative z-10">
        {local.children}
      </div>

      {/* Floating Particles */}
      <div class="absolute inset-0 pointer-events-none">
        {Array.from({ length: 5 }, (_, index) => (
          <div
            class="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
            style={{
              top: `${20 + index * 15}%`,
              left: `${10 + index * 20}%`,
              'animation-delay': `${index * 0.5}s`,
              'animation-duration': `${2 + index * 0.5}s`
            }}
          />
        ))}
      </div>
    </div>
  )
}
