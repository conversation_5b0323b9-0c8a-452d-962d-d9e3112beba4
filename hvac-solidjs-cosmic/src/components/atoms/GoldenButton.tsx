import { type Component, type JSX, splitProps, createSignal, onMount } from 'solid-js'
import { useCosmicButtonSpring } from '../../lib/motion/cosmicSprings'

export interface GoldenButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'cosmic' | 'golden' | 'divine' | 'glass'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  glow?: boolean
  physics?: boolean
  springMotion?: boolean // Enable M3 Expressive spring motion
  loading?: boolean // Loading state with cosmic spinner
  success?: boolean // Success state with checkmark animation
  error?: boolean // Error state with shake animation
  hapticFeedback?: boolean // Haptic-like visual feedback
  children: JSX.Element
}

export const GoldenButton: Component<GoldenButtonProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant',
    'size',
    'glow',
    'physics',
    'springMotion',
    'loading',
    'success',
    'error',
    'hapticFeedback',
    'children',
    'class'
  ])

  // 🎭 Enhanced State Management
  const [isPressed, setIsPressed] = createSignal(false)
  const [ripples, setRipples] = createSignal<Array<{ id: number, x: number, y: number }>>([])
  let rippleId = 0

  // 🌟 M3 Expressive Spring Motion
  const springs = local.springMotion ? useCosmicButtonSpring() : null
  let buttonRef: HTMLButtonElement | undefined

  const getVariantClasses = () => {
    switch (local.variant) {
      case 'cosmic':
        return 'bg-gradient-to-r from-cosmic-500 to-cosmic-600 hover:from-cosmic-600 hover:to-cosmic-700 text-white'
      case 'golden':
        return 'bg-gradient-to-r from-golden-500 to-golden-600 hover:from-golden-600 hover:to-golden-700 text-white'
      case 'divine':
        return 'bg-gradient-to-r from-divine-500 to-divine-600 hover:from-divine-600 hover:to-divine-700 text-white'
      case 'glass':
        return 'bg-white/10 backdrop-blur-lg border border-white/20 hover:bg-white/20 text-white'
      default:
        return 'bg-gradient-to-r from-cosmic-500 to-divine-500 hover:from-cosmic-600 hover:to-divine-600 text-white'
    }
  }

  const getSizeClasses = () => {
    switch (local.size) {
      case 'sm':
        return 'px-golden-sm py-golden-xs text-sm'
      case 'md':
        return 'px-golden-md py-golden-sm text-base'
      case 'lg':
        return 'px-golden-lg py-golden-md text-lg'
      case 'xl':
        return 'px-golden-xl py-golden-lg text-xl'
      default:
        return 'px-golden-md py-golden-sm text-base'
    }
  }

  const getGlowClasses = () => {
    if (!local.glow) return ''
    
    switch (local.variant) {
      case 'cosmic':
        return 'hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]'
      case 'golden':
        return 'hover:shadow-[0_0_20px_rgba(245,158,11,0.5)]'
      case 'divine':
        return 'hover:shadow-[0_0_20px_rgba(217,70,239,0.5)]'
      default:
        return 'hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]'
    }
  }

  const baseClasses = `
    relative overflow-hidden rounded-lg font-medium
    ${local.springMotion ? '' : 'transform transition-all duration-300 ease-out hover:scale-105 active:scale-95'}
    focus:outline-none focus:ring-2 focus:ring-white/50
    cursor-pointer select-none
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${getGlowClasses()}
    ${local.physics ? 'hover:animate-bounce active:animate-pulse' : ''}
    ${local.class || ''}
  `.trim().replace(/\s+/g, ' ')

  // 🎯 Enhanced Event Handlers with Ripple Effects
  const createRipple = (event: MouseEvent) => {
    if (!buttonRef || local.loading) return

    const rect = buttonRef.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    const newRipple = { id: rippleId++, x, y }
    setRipples(prev => [...prev, newRipple])

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== newRipple.id))
    }, 600)
  }

  const handleMouseEnter = (event: MouseEvent) => {
    if (springs) springs.hover()
    if (others.onMouseEnter) others.onMouseEnter(event)
  }

  const handleMouseLeave = (event: MouseEvent) => {
    if (springs) springs.reset()
    setIsPressed(false)
    if (others.onMouseLeave) others.onMouseLeave(event)
  }

  const handleMouseDown = (event: MouseEvent) => {
    if (local.loading) return

    setIsPressed(true)
    if (local.hapticFeedback) createRipple(event)
    if (springs) springs.press()
    if (others.onMouseDown) others.onMouseDown(event)
  }

  const handleMouseUp = (event: MouseEvent) => {
    setIsPressed(false)
    if (springs) springs.hover()
    if (others.onMouseUp) others.onMouseUp(event)
  }

  const handleClick = (event: MouseEvent) => {
    if (local.loading) {
      event.preventDefault()
      return
    }
    if (others.onClick) others.onClick(event)
  }

  // 🌟 Dynamic Style with Spring Values
  const dynamicStyle = () => {
    if (!springs) return {}
    return {
      transform: `scale(${springs.scale.get()})`,
      opacity: springs.opacity.get()
    }
  }

  return (
    <button
      ref={buttonRef}
      class={`${baseClasses} ${local.loading ? 'cursor-wait' : ''} ${local.error ? 'animate-shake' : ''}`}
      style={dynamicStyle()}
      disabled={local.loading || local.disabled}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onClick={handleClick}
      {...others}
    >
      {/* Enhanced Button Content */}
      <div class={`relative z-10 flex items-center justify-center transition-all duration-300 ${
        local.loading ? 'opacity-0' : 'opacity-100'
      }`}>
        {local.children}
      </div>

      {/* Loading State */}
      {local.loading && (
        <div class="absolute inset-0 flex items-center justify-center z-20">
          <div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
        </div>
      )}

      {/* Success State */}
      {local.success && (
        <div class="absolute inset-0 flex items-center justify-center z-20">
          <svg class="w-5 h-5 text-white animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
      )}

      {/* Error State */}
      {local.error && (
        <div class="absolute inset-0 flex items-center justify-center z-20">
          <svg class="w-5 h-5 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      )}

      {/* Enhanced Ripple Effects */}
      {ripples().map((ripple) => (
        <div
          key={ripple.id}
          class="absolute pointer-events-none z-10"
          style={{
            left: `${ripple.x}px`,
            top: `${ripple.y}px`,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div class="w-0 h-0 bg-white/30 rounded-full animate-ping"
               style="animation-duration: 600ms; animation-fill-mode: forwards;" />
        </div>
      ))}

      {/* Cosmic Glow Effect */}
      <div class={`absolute inset-0 transition-all duration-300 ${
        isPressed() ? 'opacity-100 scale-95' : 'opacity-0 hover:opacity-50 scale-100'
      }`}>
        <div class="absolute inset-0 bg-gradient-to-r from-white/10 via-white/20 to-white/10 rounded-lg blur-sm" />
      </div>

      {/* Hover Shimmer Effect */}
      <div class="absolute inset-0 rounded-lg overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full hover:translate-x-full transition-transform duration-700 ease-out" />
      </div>

      {/* Press Feedback */}
      {isPressed() && (
        <div class="absolute inset-0 bg-black/10 rounded-lg transition-opacity duration-150" />
      )}
    </button>
  )
}
