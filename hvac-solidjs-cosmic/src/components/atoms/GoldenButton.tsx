import { type Component, type JSX, splitProps, createSignal, onMount } from 'solid-js'
import { useCosmicButtonSpring } from '../../lib/motion/cosmicSprings'

export interface GoldenButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'cosmic' | 'golden' | 'divine' | 'glass'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  glow?: boolean
  physics?: boolean
  springMotion?: boolean // Enable M3 Expressive spring motion
  children: JSX.Element
}

export const GoldenButton: Component<GoldenButtonProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant',
    'size',
    'glow',
    'physics',
    'springMotion',
    'children',
    'class'
  ])

  // 🌟 M3 Expressive Spring Motion
  const springs = local.springMotion ? useCosmicButtonSpring() : null
  let buttonRef: HTMLButtonElement | undefined

  const getVariantClasses = () => {
    switch (local.variant) {
      case 'cosmic':
        return 'bg-gradient-to-r from-cosmic-500 to-cosmic-600 hover:from-cosmic-600 hover:to-cosmic-700 text-white'
      case 'golden':
        return 'bg-gradient-to-r from-golden-500 to-golden-600 hover:from-golden-600 hover:to-golden-700 text-white'
      case 'divine':
        return 'bg-gradient-to-r from-divine-500 to-divine-600 hover:from-divine-600 hover:to-divine-700 text-white'
      case 'glass':
        return 'bg-white/10 backdrop-blur-lg border border-white/20 hover:bg-white/20 text-white'
      default:
        return 'bg-gradient-to-r from-cosmic-500 to-divine-500 hover:from-cosmic-600 hover:to-divine-600 text-white'
    }
  }

  const getSizeClasses = () => {
    switch (local.size) {
      case 'sm':
        return 'px-golden-sm py-golden-xs text-sm'
      case 'md':
        return 'px-golden-md py-golden-sm text-base'
      case 'lg':
        return 'px-golden-lg py-golden-md text-lg'
      case 'xl':
        return 'px-golden-xl py-golden-lg text-xl'
      default:
        return 'px-golden-md py-golden-sm text-base'
    }
  }

  const getGlowClasses = () => {
    if (!local.glow) return ''
    
    switch (local.variant) {
      case 'cosmic':
        return 'hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]'
      case 'golden':
        return 'hover:shadow-[0_0_20px_rgba(245,158,11,0.5)]'
      case 'divine':
        return 'hover:shadow-[0_0_20px_rgba(217,70,239,0.5)]'
      default:
        return 'hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]'
    }
  }

  const baseClasses = `
    relative overflow-hidden rounded-lg font-medium
    ${local.springMotion ? '' : 'transform transition-all duration-300 ease-out hover:scale-105 active:scale-95'}
    focus:outline-none focus:ring-2 focus:ring-white/50
    cursor-pointer select-none
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${getGlowClasses()}
    ${local.physics ? 'hover:animate-bounce active:animate-pulse' : ''}
    ${local.class || ''}
  `.trim().replace(/\s+/g, ' ')

  // 🎯 Spring Motion Event Handlers
  const handleMouseEnter = () => {
    if (springs) springs.hover()
    if (others.onMouseEnter) others.onMouseEnter()
  }

  const handleMouseLeave = () => {
    if (springs) springs.reset()
    if (others.onMouseLeave) others.onMouseLeave()
  }

  const handleMouseDown = () => {
    if (springs) springs.press()
    if (others.onMouseDown) others.onMouseDown()
  }

  const handleMouseUp = () => {
    if (springs) springs.hover()
    if (others.onMouseUp) others.onMouseUp()
  }

  // 🌟 Dynamic Style with Spring Values
  const dynamicStyle = () => {
    if (!springs) return {}
    return {
      transform: `scale(${springs.scale.get()})`,
      opacity: springs.opacity.get()
    }
  }

  return (
    <button
      ref={buttonRef}
      class={baseClasses}
      style={dynamicStyle()}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      {...others}
    >
      <div class="relative z-10">
        {local.children}
      </div>

      {/* Golden Ratio Ripple Effect */}
      <div class="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300">
        <div class="absolute top-1/2 left-1/2 w-0 h-0 bg-white/20 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2" />
      </div>
    </button>
  )
}
