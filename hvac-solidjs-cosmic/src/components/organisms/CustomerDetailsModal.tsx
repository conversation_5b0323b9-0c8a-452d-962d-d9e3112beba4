// 👥 COSMIC CUSTOMER DETAILS MODAL - Comprehensive Customer Management
// Advanced customer profile with timeline, equipment, and AI insights

import { 
  type Component, 
  createSignal, 
  createEffect, 
  Show, 
  For,
  createMemo
} from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { CosmicModal } from '../molecules/CosmicModal'
import { 
  useCustomerById, 
  useEquipment, 
  useServiceJobs 
} from '../../lib/api/hooks'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  Activity,
  Wrench,
  Package,
  Brain,
  Star,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit,
  X,
  MessageSquare,
  FileText,
  Camera,
  DollarSign,
  Target,
  Zap,
  Shield,
  Heart
} from 'lucide-solid'

interface CustomerDetailsModalProps {
  customerId: string | null
  isOpen: boolean
  onClose: () => void
}

export const CustomerDetailsModal: Component<CustomerDetailsModalProps> = (props) => {
  const [activeTab, setActiveTab] = createSignal<'overview' | 'equipment' | 'services' | 'timeline' | 'ai-insights'>('overview')
  
  // 🚀 API Integration
  const customer = useCustomerById(props.customerId || '')
  const customerEquipment = useEquipment({ customerId: props.customerId })
  const customerServices = useServiceJobs({ customerId: props.customerId })

  // 🧠 AI Customer Analysis
  const customerAnalysis = createMemo(() => {
    const customerData = customer.data
    if (!customerData) return null

    return {
      healthScore: 92,
      riskLevel: 'low',
      lifetimeValue: 45000,
      nextAction: 'Schedule quarterly maintenance',
      churnRisk: 15,
      upsellOpportunity: 'Premium service plan',
      satisfaction: 4.8,
      engagement: 'high',
      predictedRevenue: 15000,
      recommendations: [
        'Schedule preventive maintenance for HVAC system',
        'Offer energy efficiency audit',
        'Propose smart thermostat upgrade'
      ]
    }
  })

  // 📊 Customer Timeline Data
  const timelineData = createMemo(() => [
    {
      id: '1',
      type: 'service',
      title: 'HVAC Maintenance Completed',
      description: 'Quarterly maintenance check performed',
      date: '2024-01-10',
      icon: Wrench,
      color: 'cosmic'
    },
    {
      id: '2',
      type: 'communication',
      title: 'Email Sent',
      description: 'Maintenance reminder sent to customer',
      date: '2024-01-05',
      icon: Mail,
      color: 'golden'
    },
    {
      id: '3',
      type: 'equipment',
      title: 'Equipment Added',
      description: 'New HVAC unit registered',
      date: '2023-12-15',
      icon: Package,
      color: 'divine'
    }
  ])

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'equipment', label: 'Equipment', icon: Package },
    { id: 'services', label: 'Services', icon: Wrench },
    { id: 'timeline', label: 'Timeline', icon: Activity },
    { id: 'ai-insights', label: 'AI Insights', icon: Brain }
  ]

  return (
    <CosmicModal
      isOpen={props.isOpen}
      onClose={props.onClose}
      title="Customer Details"
      size="xl"
    >
      <Show when={customer.data}>
        {(customerData) => (
          <div class="space-y-golden-lg">
            {/* Customer Header */}
            <div class="flex items-start justify-between">
              <div class="flex items-start space-x-golden-md">
                <div class="w-16 h-16 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center">
                  <User size={32} class="text-white" />
                </div>
                
                <div>
                  <h2 class="text-2xl font-bold text-white mb-golden-xs">
                    {customerData().name}
                  </h2>
                  <p class="text-white/70 mb-golden-sm">{customerData().email}</p>
                  
                  <div class="flex items-center space-x-golden-md text-sm">
                    <div class="flex items-center space-x-golden-xs text-white/70">
                      <Phone size={14} />
                      <span>{customerData().phone || 'No phone'}</span>
                    </div>
                    <div class="flex items-center space-x-golden-xs text-white/70">
                      <MapPin size={14} />
                      <span>{customerData().address || 'No address'}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-golden-sm">
                <Show when={customerAnalysis()}>
                  {(analysis) => (
                    <div class="text-right">
                      <div class="flex items-center space-x-golden-xs mb-golden-xs">
                        <Brain size={14} class="text-divine-400" />
                        <span class="text-divine-400 text-sm font-medium">AI Score</span>
                      </div>
                      <div class="text-2xl font-bold text-green-400">
                        {analysis().healthScore}%
                      </div>
                    </div>
                  )}
                </Show>

                <GoldenButton variant="cosmic" size="sm" glow>
                  <Edit size={14} class="mr-golden-xs" />
                  Edit
                </GoldenButton>
              </div>
            </div>

            {/* Tab Navigation */}
            <div class="border-b border-white/20">
              <div class="flex space-x-golden-md">
                <For each={tabs}>
                  {(tab) => {
                    const Icon = tab.icon
                    const isActive = activeTab() === tab.id
                    return (
                      <button
                        class={`flex items-center space-x-golden-xs px-golden-md py-golden-sm border-b-2 transition-all duration-200 ${
                          isActive
                            ? 'border-cosmic-400 text-cosmic-400'
                            : 'border-transparent text-white/70 hover:text-white hover:border-white/30'
                        }`}
                        onClick={() => setActiveTab(tab.id as any)}
                      >
                        <Icon size={16} />
                        <span class="font-medium">{tab.label}</span>
                      </button>
                    )
                  }}
                </For>
              </div>
            </div>

            {/* Tab Content */}
            <div class="min-h-[400px]">
              {/* Overview Tab */}
              <Show when={activeTab() === 'overview'}>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-golden-lg">
                  {/* Customer Info */}
                  <CosmicCard variant="glass" size="md" glow>
                    <h3 class="text-lg font-bold text-white mb-golden-md">Customer Information</h3>
                    
                    <div class="space-y-golden-sm">
                      <div class="flex justify-between">
                        <span class="text-white/70">Status:</span>
                        <span class="text-green-400 font-medium">
                          {customerData().status}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-white/70">Customer Since:</span>
                        <span class="text-white">
                          {new Date(customerData().createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-white/70">Last Contact:</span>
                        <span class="text-white">
                          {new Date(customerData().updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CosmicCard>

                  {/* AI Insights Preview */}
                  <Show when={customerAnalysis()}>
                    {(analysis) => (
                      <CosmicCard variant="divine" size="md" glow>
                        <h3 class="text-lg font-bold text-white mb-golden-md">
                          <Brain size={20} class="inline mr-golden-xs" />
                          AI Insights
                        </h3>
                        
                        <div class="space-y-golden-sm">
                          <div class="flex justify-between">
                            <span class="text-white/70">Lifetime Value:</span>
                            <span class="text-golden-400 font-bold">
                              ${analysis().lifetimeValue.toLocaleString()}
                            </span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-white/70">Churn Risk:</span>
                            <span class="text-green-400 font-medium">
                              {analysis().churnRisk}% (Low)
                            </span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-white/70">Satisfaction:</span>
                            <div class="flex items-center space-x-golden-xs">
                              <Star size={14} class="text-yellow-400" />
                              <span class="text-white">{analysis().satisfaction}/5</span>
                            </div>
                          </div>
                        </div>

                        <div class="mt-golden-md p-golden-sm bg-divine-500/10 rounded-lg border border-divine-400/20">
                          <p class="text-divine-400 text-sm font-medium mb-golden-xs">
                            Next Recommended Action:
                          </p>
                          <p class="text-white/70 text-sm">{analysis().nextAction}</p>
                        </div>
                      </CosmicCard>
                    )}
                  </Show>
                </div>
              </Show>

              {/* Equipment Tab */}
              <Show when={activeTab() === 'equipment'}>
                <div class="space-y-golden-md">
                  <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-white">Customer Equipment</h3>
                    <GoldenButton variant="cosmic" size="sm" glow>
                      <Package size={14} class="mr-golden-xs" />
                      Add Equipment
                    </GoldenButton>
                  </div>

                  <Show
                    when={!customerEquipment.isLoading && customerEquipment.data}
                    fallback={
                      <div class="text-center py-golden-xl">
                        <Package size={48} class="text-white/30 mx-auto mb-golden-md" />
                        <p class="text-white/70">Loading equipment...</p>
                      </div>
                    }
                  >
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-golden-md">
                      <For each={customerEquipment.data?.data || []}>
                        {(equipment) => (
                          <CosmicCard variant="glass" size="sm" glow hover3d>
                            <div class="flex items-start justify-between">
                              <div>
                                <h4 class="font-bold text-white mb-golden-xs">
                                  {equipment.name}
                                </h4>
                                <p class="text-white/70 text-sm mb-golden-xs">
                                  {equipment.manufacturer} {equipment.model}
                                </p>
                                <div class="flex items-center space-x-golden-xs">
                                  <div class={`w-2 h-2 rounded-full ${
                                    equipment.healthScore >= 90 ? 'bg-green-400' :
                                    equipment.healthScore >= 75 ? 'bg-yellow-400' : 'bg-red-400'
                                  }`} />
                                  <span class="text-white/70 text-xs">
                                    Health: {equipment.healthScore}%
                                  </span>
                                </div>
                              </div>
                              <Package size={20} class="text-cosmic-400" />
                            </div>
                          </CosmicCard>
                        )}
                      </For>
                    </div>
                  </Show>
                </div>
              </Show>

              {/* Services Tab */}
              <Show when={activeTab() === 'services'}>
                <div class="space-y-golden-md">
                  <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-white">Service History</h3>
                    <GoldenButton variant="golden" size="sm" glow>
                      <Wrench size={14} class="mr-golden-xs" />
                      New Service
                    </GoldenButton>
                  </div>

                  <div class="space-y-golden-sm">
                    <For each={customerServices.data?.data || []}>
                      {(service) => (
                        <CosmicCard variant="glass" size="sm" glow>
                          <div class="flex items-center justify-between">
                            <div>
                              <h4 class="font-bold text-white mb-golden-xs">
                                {service.title}
                              </h4>
                              <p class="text-white/70 text-sm mb-golden-xs">
                                {service.description}
                              </p>
                              <div class="flex items-center space-x-golden-md text-xs text-white/50">
                                <span>ID: {service.id}</span>
                                <span>Status: {service.status}</span>
                                <span>Date: {service.scheduledDate}</span>
                              </div>
                            </div>
                            <div class={`px-golden-sm py-golden-xs rounded-full text-xs font-medium ${
                              service.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                              service.status === 'in-progress' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-blue-500/20 text-blue-400'
                            }`}>
                              {service.status}
                            </div>
                          </div>
                        </CosmicCard>
                      )}
                    </For>
                  </div>
                </div>
              </Show>

              {/* Timeline Tab */}
              <Show when={activeTab() === 'timeline'}>
                <div class="space-y-golden-md">
                  <h3 class="text-lg font-bold text-white">Activity Timeline</h3>
                  
                  <div class="space-y-golden-sm">
                    <For each={timelineData()}>
                      {(item) => {
                        const Icon = item.icon
                        return (
                          <div class="flex items-start space-x-golden-md">
                            <div class={`w-10 h-10 bg-gradient-to-r ${
                              item.color === 'cosmic' ? 'from-cosmic-400 to-cosmic-600' :
                              item.color === 'golden' ? 'from-golden-400 to-golden-600' :
                              'from-divine-400 to-divine-600'
                            } rounded-full flex items-center justify-center`}>
                              <Icon size={16} class="text-white" />
                            </div>
                            
                            <div class="flex-1">
                              <div class="bg-white/5 rounded-lg p-golden-sm border border-white/10">
                                <div class="flex items-center justify-between mb-golden-xs">
                                  <h4 class="font-medium text-white">{item.title}</h4>
                                  <span class="text-white/50 text-xs">{item.date}</span>
                                </div>
                                <p class="text-white/70 text-sm">{item.description}</p>
                              </div>
                            </div>
                          </div>
                        )
                      }}
                    </For>
                  </div>
                </div>
              </Show>

              {/* AI Insights Tab */}
              <Show when={activeTab() === 'ai-insights' && customerAnalysis()}>
                {(analysis) => (
                  <div class="space-y-golden-lg">
                    <h3 class="text-lg font-bold text-white">
                      <Brain size={20} class="inline mr-golden-xs" />
                      AI-Powered Customer Analysis
                    </h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-golden-lg">
                      {/* Recommendations */}
                      <CosmicCard variant="divine" size="md" glow>
                        <h4 class="font-bold text-white mb-golden-md">AI Recommendations</h4>
                        <div class="space-y-golden-sm">
                          <For each={analysis().recommendations}>
                            {(recommendation) => (
                              <div class="flex items-start space-x-golden-sm">
                                <Target size={14} class="text-divine-400 mt-0.5" />
                                <p class="text-white/70 text-sm">{recommendation}</p>
                              </div>
                            )}
                          </For>
                        </div>
                      </CosmicCard>

                      {/* Predictions */}
                      <CosmicCard variant="cosmic" size="md" glow>
                        <h4 class="font-bold text-white mb-golden-md">Predictions</h4>
                        <div class="space-y-golden-sm">
                          <div class="flex justify-between">
                            <span class="text-white/70">Predicted Revenue:</span>
                            <span class="text-cosmic-400 font-bold">
                              ${analysis().predictedRevenue.toLocaleString()}
                            </span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-white/70">Upsell Opportunity:</span>
                            <span class="text-white">{analysis().upsellOpportunity}</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-white/70">Engagement Level:</span>
                            <span class="text-green-400 font-medium capitalize">
                              {analysis().engagement}
                            </span>
                          </div>
                        </div>
                      </CosmicCard>
                    </div>
                  </div>
                )}
              </Show>
            </div>
          </div>
        )}
      </Show>
    </CosmicModal>
  )
}
