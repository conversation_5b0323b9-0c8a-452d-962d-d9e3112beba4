// 🔧 COSMIC EQUIPMENT REGISTRY - Advanced Equipment Management
// Complete equipment management system with AI insights and cosmic design

import { 
  type Component, 
  createSignal, 
  createEffect, 
  onMount, 
  Show, 
  For,
  createMemo
} from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { EquipmentCard } from '../molecules/EquipmentCard'
import { 
  useEquipment, 
  useEquipmentAnalytics,
  useGenerateQRCode 
} from '../../lib/api/hooks'
import type { EquipmentFilters, EquipmentCategory, EquipmentStatus } from '../../types/equipment'
import {
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  BarChart3,
  Wrench,
  AlertTriangle,
  CheckCircle,
  Package,
  TrendingUp,
  Brain,
  Zap,
  Shield,
  Calendar,
  Eye,
  Settings,
  Sparkles,
  Activity,
  Star
} from 'lucide-solid'

export const EquipmentRegistry: Component = () => {
  // 🌟 Cosmic State Management
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedCategory, setSelectedCategory] = createSignal<EquipmentCategory | 'ALL'>('ALL')
  const [selectedStatus, setSelectedStatus] = createSignal<EquipmentStatus | 'ALL'>('ALL')
  const [showFilters, setShowFilters] = createSignal(false)
  const [viewMode, setViewMode] = createSignal<'grid' | 'list'>('grid')
  const [cosmicMode, setCosmicMode] = createSignal(true)

  // 🚀 API Integration
  const filters = createMemo((): EquipmentFilters => ({
    search: searchTerm() || undefined,
    category: selectedCategory() !== 'ALL' ? [selectedCategory() as EquipmentCategory] : undefined,
    status: selectedStatus() !== 'ALL' ? [selectedStatus() as EquipmentStatus] : undefined
  }))

  const equipment = useEquipment(filters())
  const analytics = useEquipmentAnalytics()
  const generateQR = useGenerateQRCode()

  // 🎭 Cosmic Entrance Animation
  onMount(() => {
    setTimeout(() => setIsLoaded(true), 300)
  })

  // 🔧 Equipment Categories for Filter
  const categories: Array<{ value: EquipmentCategory | 'ALL', label: string }> = [
    { value: 'ALL', label: 'All Categories' },
    { value: 'HVAC_UNIT', label: 'HVAC Units' },
    { value: 'AIR_HANDLER', label: 'Air Handlers' },
    { value: 'CONDENSER', label: 'Condensers' },
    { value: 'EVAPORATOR', label: 'Evaporators' },
    { value: 'FURNACE', label: 'Furnaces' },
    { value: 'HEAT_PUMP', label: 'Heat Pumps' },
    { value: 'BOILER', label: 'Boilers' },
    { value: 'CHILLER', label: 'Chillers' },
    { value: 'THERMOSTAT', label: 'Thermostats' },
    { value: 'DUCTWORK', label: 'Ductwork' },
    { value: 'VENTILATION', label: 'Ventilation' },
    { value: 'FILTER_SYSTEM', label: 'Filter Systems' },
    { value: 'CONTROL_SYSTEM', label: 'Control Systems' }
  ]

  // 🎯 Equipment Status for Filter
  const statuses: Array<{ value: EquipmentStatus | 'ALL', label: string }> = [
    { value: 'ALL', label: 'All Status' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'INACTIVE', label: 'Inactive' },
    { value: 'MAINTENANCE', label: 'In Maintenance' },
    { value: 'NEEDS_REPAIR', label: 'Needs Repair' },
    { value: 'NEEDS_REPLACEMENT', label: 'Needs Replacement' },
    { value: 'DECOMMISSIONED', label: 'Decommissioned' }
  ]

  // 🎯 Handle Equipment Actions
  const handleViewEquipment = (id: string) => {
    console.log('View equipment:', id)
    // TODO: Navigate to equipment details
  }

  const handleEditEquipment = (id: string) => {
    console.log('Edit equipment:', id)
    // TODO: Open equipment edit modal
  }

  const handleMaintenanceSchedule = (id: string) => {
    console.log('Schedule maintenance for:', id)
    // TODO: Open maintenance scheduling modal
  }

  const handleGenerateQR = async (id: string) => {
    try {
      const result = await generateQR.mutateAsync(id)
      console.log('QR Code generated:', result)
      // TODO: Show QR code modal
    } catch (error) {
      console.error('Failed to generate QR code:', error)
    }
  }

  return (
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-golden-lg">
      {/* 🌌 Cosmic Background Effects */}
      <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-cosmic-500/10 rounded-full blur-3xl animate-cosmic-float"></div>
        <div class="absolute top-3/4 right-1/4 w-96 h-96 bg-golden-500/10 rounded-full blur-3xl animate-cosmic-float" style="animation-delay: 2s"></div>
        <div class="absolute top-1/2 left-1/2 w-96 h-96 bg-divine-500/10 rounded-full blur-3xl animate-cosmic-float" style="animation-delay: 4s"></div>
      </div>

      {/* 🎯 Header Section */}
      <div class={`relative z-10 transition-all duration-1000 ${
        isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-golden-lg">
          <div>
            <h1 class="text-4xl font-bold bg-gradient-to-r from-cosmic-400 via-golden-400 to-divine-400 bg-clip-text text-transparent mb-golden-sm">
              🔧 Equipment Registry
            </h1>
            <p class="text-white/70 text-lg">
              Advanced equipment management with AI-powered insights
            </p>
          </div>
          
          <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
            {/* Cosmic Mode Toggle */}
            <GoldenButton
              variant={cosmicMode() ? "cosmic" : "golden"}
              size="md"
              glow
              springMotion={true}
              onClick={() => setCosmicMode(!cosmicMode())}
            >
              <Sparkles size={16} class="mr-golden-xs" />
              {cosmicMode() ? 'Cosmic' : 'Standard'}
            </GoldenButton>

            {/* Add Equipment */}
            <GoldenButton
              variant="divine"
              size="md"
              glow
              springMotion={true}
            >
              <Plus size={16} class="mr-golden-xs" />
              Add Equipment
            </GoldenButton>

            {/* Refresh */}
            <GoldenButton
              variant="cosmic"
              size="md"
              glow
              springMotion={true}
              onClick={() => {
                equipment.refetch()
                analytics.refetch()
              }}
              disabled={equipment.isFetching}
            >
              <RefreshCw
                size={16}
                class={`mr-golden-xs ${equipment.isFetching ? 'animate-spin' : ''}`}
              />
              Refresh
            </GoldenButton>
          </div>
        </div>
      </div>

      {/* 📊 Analytics Overview */}
      <div class={`relative z-10 transition-all duration-1000 delay-200 ${
        isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        <Show
          when={!analytics.isLoading && analytics.data}
          fallback={
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md mb-golden-lg">
              {Array.from({ length: 4 }, (_, index) => (
                <CosmicCard variant="glass" size="md" glow>
                  <div class="animate-pulse">
                    <div class="h-4 bg-white/20 rounded mb-golden-xs"></div>
                    <div class="h-8 bg-white/20 rounded mb-golden-xs"></div>
                    <div class="h-4 bg-white/20 rounded w-1/2"></div>
                  </div>
                </CosmicCard>
              ))}
            </div>
          }
        >
          {(data) => (
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md mb-golden-lg">
              {/* Total Equipment */}
              <CosmicCard variant={cosmicMode() ? "cosmic" : "glass"} size="md" glow hover3d springMotion={true}>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-white/70 text-sm mb-golden-xs">Total Equipment</p>
                    <p class="text-3xl font-bold text-white">{data().totalEquipment}</p>
                    <p class="text-cosmic-400 text-sm">Registered units</p>
                  </div>
                  <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-cosmic-600 rounded-lg flex items-center justify-center">
                    <Package size={24} class="text-white" />
                  </div>
                </div>
              </CosmicCard>

              {/* Active Equipment */}
              <CosmicCard variant={cosmicMode() ? "cosmic" : "glass"} size="md" glow hover3d springMotion={true}>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-white/70 text-sm mb-golden-xs">Active Equipment</p>
                    <p class="text-3xl font-bold text-white">{data().activeEquipment}</p>
                    <p class="text-green-400 text-sm">Currently operational</p>
                  </div>
                  <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                    <CheckCircle size={24} class="text-white" />
                  </div>
                </div>
              </CosmicCard>

              {/* Maintenance Due */}
              <CosmicCard variant={cosmicMode() ? "cosmic" : "glass"} size="md" glow hover3d springMotion={true}>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-white/70 text-sm mb-golden-xs">Maintenance Due</p>
                    <p class="text-3xl font-bold text-white">{data().maintenanceDue}</p>
                    <p class="text-yellow-400 text-sm">Requires attention</p>
                  </div>
                  <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                    <Wrench size={24} class="text-white" />
                  </div>
                </div>
              </CosmicCard>

              {/* Average Health */}
              <CosmicCard variant={cosmicMode() ? "cosmic" : "glass"} size="md" glow hover3d springMotion={true}>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-white/70 text-sm mb-golden-xs">Avg Health Score</p>
                    <p class="text-3xl font-bold text-white">{Math.round(data().averageHealthScore)}%</p>
                    <p class="text-divine-400 text-sm">AI-calculated</p>
                  </div>
                  <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-divine-600 rounded-lg flex items-center justify-center">
                    <Brain size={24} class="text-white" />
                  </div>
                </div>
              </CosmicCard>
            </div>
          )}
        </Show>
      </div>

      {/* 🔍 Search & Filters */}
      <div class={`relative z-10 transition-all duration-1000 delay-400 ${
        isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        <CosmicCard variant="glass" size="md" glow class="mb-golden-lg">
          <div class="flex flex-col lg:flex-row lg:items-center space-y-golden-md lg:space-y-0 lg:space-x-golden-md">
            {/* Search */}
            <div class="flex-1 relative">
              <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search equipment by name, model, or serial number..."
                value={searchTerm()}
                onInput={(e) => setSearchTerm(e.currentTarget.value)}
                class="w-full bg-white/5 border border-white/20 rounded-lg pl-12 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:border-cosmic-400 transition-colors"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory()}
              onChange={(e) => setSelectedCategory(e.currentTarget.value as EquipmentCategory | 'ALL')}
              class="bg-white/5 border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:border-cosmic-400 transition-colors"
            >
              <For each={categories}>
                {(category) => (
                  <option value={category.value} class="bg-slate-800 text-white">
                    {category.label}
                  </option>
                )}
              </For>
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus()}
              onChange={(e) => setSelectedStatus(e.currentTarget.value as EquipmentStatus | 'ALL')}
              class="bg-white/5 border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:border-cosmic-400 transition-colors"
            >
              <For each={statuses}>
                {(status) => (
                  <option value={status.value} class="bg-slate-800 text-white">
                    {status.label}
                  </option>
                )}
              </For>
            </select>

            {/* View Mode Toggle */}
            <div class="flex items-center space-x-golden-xs">
              <GoldenButton
                variant={viewMode() === 'grid' ? 'cosmic' : 'glass'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <BarChart3 size={16} />
              </GoldenButton>
              <GoldenButton
                variant={viewMode() === 'list' ? 'cosmic' : 'glass'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <Activity size={16} />
              </GoldenButton>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* 🔧 Equipment Grid */}
      <div class={`relative z-10 transition-all duration-1000 delay-600 ${
        isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        <Show
          when={!equipment.isLoading && equipment.data}
          fallback={
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-golden-lg">
              {Array.from({ length: 6 }, (_, index) => (
                <CosmicCard variant="glass" size="lg" glow>
                  <div class="animate-pulse space-y-golden-md">
                    <div class="flex items-center space-x-golden-sm">
                      <div class="w-12 h-12 bg-white/20 rounded-lg"></div>
                      <div class="flex-1">
                        <div class="h-4 bg-white/20 rounded mb-golden-xs"></div>
                        <div class="h-3 bg-white/20 rounded w-2/3"></div>
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-golden-sm">
                      <div class="h-16 bg-white/20 rounded"></div>
                      <div class="h-16 bg-white/20 rounded"></div>
                    </div>
                  </div>
                </CosmicCard>
              ))}
            </div>
          }
        >
          {(data) => (
            <div class={`grid gap-golden-lg ${
              viewMode() === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}>
              <For each={data().data}>
                {(equipmentItem) => (
                  <EquipmentCard
                    equipment={equipmentItem}
                    onView={handleViewEquipment}
                    onEdit={handleEditEquipment}
                    onMaintenance={handleMaintenanceSchedule}
                    onGenerateQR={handleGenerateQR}
                    variant={viewMode() === 'list' ? 'compact' : 'default'}
                    cosmicMode={cosmicMode()}
                  />
                )}
              </For>
            </div>
          )}
        </Show>
      </div>
    </div>
  )
}
