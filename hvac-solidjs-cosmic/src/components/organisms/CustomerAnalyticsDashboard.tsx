import { type Component, createSignal, onMount, For, Show, createMemo } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Users,
  Star,
  Brain,
  Activity,
  Calendar,
  Clock,
  Target,
  Award,
  Zap,
  Heart,
  Globe,
  Sparkles,
  Database,
  Cpu,
  Signal,
  Wifi,
  Lightning,
  Eye,
  Filter,
  Download,
  RefreshCw,
  Settings,
  Layers,
  MoreVertical
} from 'lucide-solid'

interface CustomerMetrics {
  totalCustomers: number
  activeCustomers: number
  newThisMonth: number
  avgScore: number
  topPerformers: number
  needsAttention: number
  conversionRate: number
  retentionRate: number
  satisfactionScore: number
  responseTime: number
}

interface ChartData {
  name: string
  value: number
  color: string
  trend?: number
}

export const CustomerAnalyticsDashboard: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [selectedTimeframe, setSelectedTimeframe] = createSignal('30d')
  const [selectedMetric, setSelectedMetric] = createSignal('all')
  const [showAdvanced, setShowAdvanced] = createSignal(false)

  // Mock data - in real app this would come from API
  const metrics = createMemo((): CustomerMetrics => ({
    totalCustomers: 1247,
    activeCustomers: 892,
    newThisMonth: 156,
    avgScore: 78,
    topPerformers: 234,
    needsAttention: 89,
    conversionRate: 24.5,
    retentionRate: 87.3,
    satisfactionScore: 4.6,
    responseTime: 2.4
  }))

  const statusDistribution = createMemo((): ChartData[] => [
    { name: 'Active', value: 892, color: 'from-green-400 to-emerald-500', trend: 12.5 },
    { name: 'Prospects', value: 234, color: 'from-yellow-400 to-orange-500', trend: 8.3 },
    { name: 'Inactive', value: 89, color: 'from-red-400 to-pink-500', trend: -5.2 },
    { name: 'Pending', value: 32, color: 'from-blue-400 to-cyan-500', trend: 15.7 }
  ])

  const scoreDistribution = createMemo((): ChartData[] => [
    { name: 'High (80+)', value: 234, color: 'from-green-400 to-emerald-500' },
    { name: 'Medium (60-79)', value: 567, color: 'from-yellow-400 to-orange-500' },
    { name: 'Low (<60)', value: 446, color: 'from-red-400 to-pink-500' }
  ])

  const monthlyTrends = createMemo(() => [
    { month: 'Jan', customers: 1089, score: 76 },
    { month: 'Feb', customers: 1134, score: 77 },
    { month: 'Mar', customers: 1178, score: 78 },
    { month: 'Apr', customers: 1203, score: 77 },
    { month: 'May', customers: 1247, score: 78 }
  ])

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 300)
  })

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'customers': return Users
      case 'score': return Brain
      case 'activity': return Activity
      case 'conversion': return Target
      default: return BarChart3
    }
  }

  const formatNumber = (num: number, decimals = 0) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num)
  }

  const formatPercentage = (num: number) => {
    return `${formatNumber(num, 1)}%`
  }

  return (
    <div class="space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
            <BarChart3 size={24} class="text-white" />
          </div>
          <div>
            <h2 class="text-2xl font-bold text-white">Customer Analytics</h2>
            <p class="text-white/70">AI-powered insights and performance metrics</p>
          </div>
        </div>

        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          {/* Timeframe Selector */}
          <select
            class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
            value={selectedTimeframe()}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>

          <GoldenButton
            variant={showAdvanced() ? 'cosmic' : 'glass'}
            size="md"
            glow={showAdvanced()}
            onClick={() => setShowAdvanced(!showAdvanced())}
          >
            <Settings size={16} class="mr-golden-xs" />
            Advanced
          </GoldenButton>

          <GoldenButton variant="divine" size="md" glow>
            <Download size={16} class="mr-golden-xs" />
            Export
          </GoldenButton>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow hover3d>
          <div class="text-center">
            <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-cosmic-600 rounded-full flex items-center justify-center mx-auto mb-golden-sm">
              <Users size={24} class="text-white" />
            </div>
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {formatNumber(metrics().totalCustomers)}
            </div>
            <div class="text-white/70 text-sm">Total Customers</div>
            <div class="flex items-center justify-center space-x-golden-xs mt-golden-xs">
              <TrendingUp size={12} class="text-green-400" />
              <span class="text-green-400 text-xs">+12.5%</span>
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="golden" size="md" glow hover3d>
          <div class="text-center">
            <div class="w-12 h-12 bg-gradient-to-r from-golden-400 to-golden-600 rounded-full flex items-center justify-center mx-auto mb-golden-sm">
              <Activity size={24} class="text-white" />
            </div>
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {formatNumber(metrics().activeCustomers)}
            </div>
            <div class="text-white/70 text-sm">Active Customers</div>
            <div class="flex items-center justify-center space-x-golden-xs mt-golden-xs">
              <TrendingUp size={12} class="text-green-400" />
              <span class="text-green-400 text-xs">+8.3%</span>
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="divine" size="md" glow hover3d>
          <div class="text-center">
            <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-divine-600 rounded-full flex items-center justify-center mx-auto mb-golden-sm">
              <Brain size={24} class="text-white" />
            </div>
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {formatNumber(metrics().avgScore)}
            </div>
            <div class="text-white/70 text-sm">Avg AI Score</div>
            <div class="flex items-center justify-center space-x-golden-xs mt-golden-xs">
              <TrendingUp size={12} class="text-green-400" />
              <span class="text-green-400 text-xs">+2.1%</span>
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="glass" size="md" glow hover3d>
          <div class="text-center">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-golden-sm">
              <Target size={24} class="text-white" />
            </div>
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {formatPercentage(metrics().conversionRate)}
            </div>
            <div class="text-white/70 text-sm">Conversion Rate</div>
            <div class="flex items-center justify-center space-x-golden-xs mt-golden-xs">
              <TrendingUp size={12} class="text-green-400" />
              <span class="text-green-400 text-xs">+3.2%</span>
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="cosmic" size="md" glow hover3d>
          <div class="text-center">
            <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-golden-sm">
              <Heart size={24} class="text-white" />
            </div>
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {formatNumber(metrics().satisfactionScore, 1)}
            </div>
            <div class="text-white/70 text-sm">Satisfaction</div>
            <div class="flex items-center justify-center space-x-golden-xs mt-golden-xs">
              <Star size={12} class="text-yellow-400" />
              <span class="text-yellow-400 text-xs">Excellent</span>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Charts and Analytics */}
      <div
        class={`grid grid-cols-1 lg:grid-cols-2 gap-golden-lg transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {/* Customer Status Distribution */}
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">Customer Status Distribution</h3>
            <GoldenButton variant="cosmic" size="xs" glow>
              <Eye size={12} />
            </GoldenButton>
          </div>

          <div class="space-y-golden-md">
            <For each={statusDistribution()}>
              {(item) => (
                <div class="space-y-golden-sm">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-golden-sm">
                      <div class={`w-3 h-3 rounded-full bg-gradient-to-r ${item.color}`}></div>
                      <span class="text-white font-medium">{item.name}</span>
                    </div>
                    <div class="flex items-center space-x-golden-sm">
                      <span class="text-white font-bold">{formatNumber(item.value)}</span>
                      <Show when={item.trend}>
                        <div class={`flex items-center space-x-1 ${
                          item.trend! > 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {item.trend! > 0 ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
                          <span class="text-xs">{Math.abs(item.trend!)}%</span>
                        </div>
                      </Show>
                    </div>
                  </div>
                  <div class="w-full bg-white/10 rounded-full h-2">
                    <div
                      class={`h-2 rounded-full bg-gradient-to-r ${item.color} transition-all duration-1000`}
                      style={{ width: `${(item.value / metrics().totalCustomers) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </CosmicCard>

        {/* AI Score Distribution */}
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">AI Score Distribution</h3>
            <div class="flex items-center space-x-golden-xs">
              <Brain size={16} class="text-purple-400" />
              <span class="text-purple-400 text-sm">AI Powered</span>
            </div>
          </div>

          <div class="space-y-golden-md">
            <For each={scoreDistribution()}>
              {(item) => (
                <div class="space-y-golden-sm">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-golden-sm">
                      <div class={`w-3 h-3 rounded-full bg-gradient-to-r ${item.color}`}></div>
                      <span class="text-white font-medium">{item.name}</span>
                    </div>
                    <span class="text-white font-bold">{formatNumber(item.value)}</span>
                  </div>
                  <div class="w-full bg-white/10 rounded-full h-2">
                    <div
                      class={`h-2 rounded-full bg-gradient-to-r ${item.color} transition-all duration-1000`}
                      style={{ width: `${(item.value / metrics().totalCustomers) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </CosmicCard>
      </div>

      {/* Monthly Trends */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">Monthly Trends</h3>
            <div class="flex items-center space-x-golden-sm">
              <GoldenButton variant="cosmic" size="xs" glow>
                <Calendar size={12} class="mr-1" />
                Monthly
              </GoldenButton>
              <GoldenButton variant="glass" size="xs" glow>
                <BarChart3 size={12} />
              </GoldenButton>
            </div>
          </div>

          <div class="grid grid-cols-5 gap-golden-md">
            <For each={monthlyTrends()}>
              {(month, index) => {
                const maxCustomers = Math.max(...monthlyTrends().map(m => m.customers))
                const height = (month.customers / maxCustomers) * 100

                return (
                  <div class="text-center">
                    <div class="mb-golden-sm">
                      <div class="h-32 flex items-end justify-center">
                        <div
                          class="w-8 bg-gradient-to-t from-cosmic-400 to-divine-400 rounded-t transition-all duration-1000 hover:from-cosmic-300 hover:to-divine-300"
                          style={{ height: `${height}%` }}
                        ></div>
                      </div>
                    </div>
                    <div class="space-y-1">
                      <div class="text-white font-medium text-sm">{month.month}</div>
                      <div class="text-white/70 text-xs">{formatNumber(month.customers)}</div>
                      <div class="flex items-center justify-center space-x-1">
                        <Brain size={10} class="text-purple-400" />
                        <span class="text-purple-400 text-xs">{month.score}</span>
                      </div>
                    </div>
                  </div>
                )
              }}
            </For>
          </div>
        </CosmicCard>
      </div>

      {/* Advanced Analytics Panel */}
      <Show when={showAdvanced()}>
        <div
          class={`transition-all duration-1000 delay-800 ${
            isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}
        >
          <CosmicCard variant="cosmic" size="lg" glow>
            <div class="flex items-center justify-between mb-golden-md">
              <div class="flex items-center space-x-golden-sm">
                <Lightning size={20} class="text-yellow-400" />
                <h3 class="text-xl font-bold text-white">Advanced AI Insights</h3>
              </div>
              <div class="flex items-center space-x-golden-xs">
                <Sparkles size={16} class="text-yellow-400" />
                <span class="text-yellow-400 text-sm">Real-time</span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md">
              <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                <div class="flex items-center space-x-golden-sm mb-golden-sm">
                  <Target size={16} class="text-green-400" />
                  <span class="text-white font-medium">Retention Rate</span>
                </div>
                <div class="text-2xl font-bold text-green-400 mb-golden-xs">
                  {formatPercentage(metrics().retentionRate)}
                </div>
                <div class="text-white/70 text-sm">Above industry avg</div>
              </div>

              <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                <div class="flex items-center space-x-golden-sm mb-golden-sm">
                  <Clock size={16} class="text-blue-400" />
                  <span class="text-white font-medium">Response Time</span>
                </div>
                <div class="text-2xl font-bold text-blue-400 mb-golden-xs">
                  {formatNumber(metrics().responseTime, 1)}h
                </div>
                <div class="text-white/70 text-sm">Excellent speed</div>
              </div>

              <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                <div class="flex items-center space-x-golden-sm mb-golden-sm">
                  <Award size={16} class="text-yellow-400" />
                  <span class="text-white font-medium">Top Performers</span>
                </div>
                <div class="text-2xl font-bold text-yellow-400 mb-golden-xs">
                  {formatNumber(metrics().topPerformers)}
                </div>
                <div class="text-white/70 text-sm">High-value customers</div>
              </div>

              <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                <div class="flex items-center space-x-golden-sm mb-golden-sm">
                  <Zap size={16} class="text-red-400" />
                  <span class="text-white font-medium">Needs Attention</span>
                </div>
                <div class="text-2xl font-bold text-red-400 mb-golden-xs">
                  {formatNumber(metrics().needsAttention)}
                </div>
                <div class="text-white/70 text-sm">Requires follow-up</div>
              </div>
            </div>
          </CosmicCard>
        </div>
      </Show>
    </div>
  )
}
