@tailwind base;
@tailwind components;
@tailwind utilities;

/* Golden Ratio CSS Variables */
:root {
  /* Golden Ratio Constants */
  --golden-ratio: 1.618;
  --golden-ratio-inverse: 0.618;

  /* Golden Ratio Spacing Scale */
  --space-golden-xs: 0.382rem;    /* 1/φ² */
  --space-golden-sm: 0.618rem;    /* 1/φ */
  --space-golden-base: 1rem;      /* 1 */
  --space-golden-md: 1.618rem;    /* φ */
  --space-golden-lg: 2.618rem;    /* φ² */
  --space-golden-xl: 4.236rem;    /* φ³ */
  --space-golden-2xl: 6.854rem;   /* φ⁴ */
  --space-golden-3xl: 11.09rem;   /* φ⁵ */

  /* Fibonacci Sequence */
  --fib-1: 0.25rem;   /* 1 */
  --fib-2: 0.5rem;    /* 2 */
  --fib-3: 0.75rem;   /* 3 */
  --fib-5: 1.25rem;   /* 5 */
  --fib-8: 2rem;      /* 8 */
  --fib-13: 3.25rem;  /* 13 */
  --fib-21: 5.25rem;  /* 21 */
  --fib-34: 8.5rem;   /* 34 */
  --fib-55: 13.75rem; /* 55 */
  --fib-89: 22.25rem; /* 89 */
  --fib-144: 36rem;   /* 144 */

  /* Cosmic Colors */
  --color-cosmic-primary: #0ea5e9;
  --color-cosmic-secondary: #d946ef;
  --color-golden-primary: #f59e0b;
  --color-golden-secondary: #d97706;
  --color-divine-primary: #d946ef;
  --color-divine-secondary: #a21caf;

  /* Animation Durations (Golden Ratio Based) */
  --duration-golden-fast: 0.236s;    /* φ⁻² */
  --duration-golden-normal: 0.382s;  /* φ⁻¹ */
  --duration-golden-slow: 0.618s;    /* 1/φ */
  --duration-golden-slower: 1s;      /* 1 */
  --duration-golden-slowest: 1.618s; /* φ */

  /* Physics-based easing curves */
  --easing-golden: cubic-bezier(0.618, 0, 0.382, 1);
  --easing-cosmic: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --easing-divine: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Base Styles with Golden Ratio */
html {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: var(--golden-ratio);
  font-size: 1rem;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%);
  color: white;
  overflow-x: hidden;
}

/* 🌌 Enhanced Cosmic Animations */
@keyframes cosmic-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes cosmic-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes cosmic-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(14, 165, 233, 0.3); }
  50% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.6), 0 0 30px rgba(14, 165, 233, 0.4); }
}

@keyframes ripple {
  0% { width: 0; height: 0; opacity: 1; }
  100% { width: 100px; height: 100px; opacity: 0; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 🎨 Enhanced Utility Classes */
.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-cosmic-float {
  animation: cosmic-float 6s ease-in-out infinite;
}

.animate-cosmic-pulse {
  animation: cosmic-pulse 2s ease-in-out infinite;
}

.animate-cosmic-glow {
  animation: cosmic-glow 3s ease-in-out infinite;
}

.animate-ripple {
  animation: ripple 0.6s ease-out forwards;
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
}
