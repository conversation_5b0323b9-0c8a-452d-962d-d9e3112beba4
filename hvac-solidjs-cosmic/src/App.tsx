import { type Component, onMount, createSignal } from 'solid-js'
import { Router, Route } from '@solidjs/router'
import { QueryClient, QueryClientProvider } from '@tanstack/solid-query'

// Layout Components
import { MainLayout } from './components/templates/MainLayout'

// Page Components
import { HomePage } from './components/pages/HomePage'
import { DashboardPage } from './components/pages/DashboardPage'
import { CustomersPage } from './components/pages/CustomersPage'
import { ServiceOrdersPage } from './components/pages/ServiceOrdersPage'
import { InventoryPage } from './components/pages/InventoryPage'
import { AnalyticsPage } from './components/pages/AnalyticsPage'
import { SettingsPage } from './components/pages/SettingsPage'
import { CosmicDashboardEnhanced } from './components/organisms/CosmicDashboardEnhanced'
import { EquipmentPage } from './components/pages/EquipmentPage'

// Global Components
import { ToastContainer, toast } from './components/molecules/CosmicToast'

// API Integration
import { cosmicConfig, healthCheck, AuthManager } from './lib/api'

// Create QueryClient for data management with cosmic configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: cosmicConfig.performance.staleTime,
      gcTime: cosmicConfig.performance.cacheTime,
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
})

const App: Component = () => {
  const [isBackendHealthy, setIsBackendHealthy] = createSignal(false)
  const [isInitialized, setIsInitialized] = createSignal(false)

  // Initialize app and check backend health
  onMount(async () => {
    try {
      // Check backend health
      const healthy = await healthCheck.checkBackend()
      setIsBackendHealthy(healthy)

      if (!healthy) {
        toast.error('Backend connection failed. Some features may not work.')
      }

      // Initialize auth manager
      const auth = AuthManager.getInstance()
      // Could restore session here if needed

      setIsInitialized(true)
    } catch (error) {
      console.error('App initialization failed:', error)
      toast.error('Application initialization failed')
      setIsInitialized(true) // Still allow app to load
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Route path="/" component={MainLayout}>
          <Route path="/" component={HomePage} />
          <Route path="/dashboard" component={DashboardPage} />
          <Route path="/dashboard-enhanced" component={CosmicDashboardEnhanced} />
          <Route path="/equipment" component={EquipmentPage} />
          <Route path="/customers" component={CustomersPage} />
          <Route path="/service-orders" component={ServiceOrdersPage} />
          <Route path="/inventory" component={InventoryPage} />
          <Route path="/analytics" component={AnalyticsPage} />
          <Route path="/settings" component={SettingsPage} />
        </Route>
      </Router>

      {/* Global Toast Container */}
      <ToastContainer toasts={toast.getToasts()} position="top-right" />

      {/* Connection Status Indicator */}
      {isInitialized() && !isBackendHealthy() && (
        <div class="fixed bottom-4 left-4 bg-red-500/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-50">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-red-300 rounded-full animate-pulse"></div>
            <span class="text-sm">Backend Offline</span>
          </div>
        </div>
      )}
    </QueryClientProvider>
  )
}

export default App
