// 🔧 COSMIC SERVICE MANAGEMENT - Comprehensive Service Types
// Advanced service order management with cosmic intelligence

// 🎯 Service Order Status
export enum ServiceOrderStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REQUIRES_APPROVAL = 'REQUIRES_APPROVAL'
}

// 🔧 Service Types
export enum ServiceType {
  INSTALLATION = 'INSTALLATION',
  MAINTENANCE = 'MAINTENANCE',
  REPAIR = 'REPAIR',
  INSPECTION = 'INSPECTION',
  EMERGENCY = 'EMERGENCY',
  CONSULTATION = 'CONSULTATION',
  UPGRADE = 'UPGRADE',
  CLEANING = 'CLEANING'
}

// ⚡ Service Priority
export enum ServicePriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
  EMERGENCY = 'EMERGENCY'
}

// 👨‍🔧 Technician Skill Level
export enum TechnicianSkillLevel {
  APPRENTICE = 'APPRENTICE',
  TECHNICIAN = 'TECHNICIAN',
  SENIOR_TECHNICIAN = 'SENIOR_TECHNICIAN',
  SPECIALIST = 'SPECIALIST',
  MASTER_TECHNICIAN = 'MASTER_TECHNICIAN'
}

// 🧰 Required Skills
export interface RequiredSkill {
  skill: string
  level: TechnicianSkillLevel
  required: boolean
  experience: number // years
}

// 👨‍🔧 Technician Profile
export interface Technician {
  id: string
  name: string
  email: string
  phone: string
  skills: RequiredSkill[]
  certifications: string[]
  availability: {
    monday: { start: string, end: string, available: boolean }
    tuesday: { start: string, end: string, available: boolean }
    wednesday: { start: string, end: string, available: boolean }
    thursday: { start: string, end: string, available: boolean }
    friday: { start: string, end: string, available: boolean }
    saturday: { start: string, end: string, available: boolean }
    sunday: { start: string, end: string, available: boolean }
  }
  currentLocation: {
    latitude: number
    longitude: number
    address: string
    lastUpdated: Date
  }
  rating: number
  completedJobs: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// 🧰 Service Parts
export interface ServicePart {
  id: string
  partNumber: string
  name: string
  description: string
  quantity: number
  unitCost: number
  totalCost: number
  supplier: string
  warrantyMonths: number
  isStocked: boolean
  estimatedDelivery?: Date
}

// 📋 Service Checklist Item
export interface ServiceChecklistItem {
  id: string
  title: string
  description: string
  isRequired: boolean
  isCompleted: boolean
  completedBy?: string
  completedAt?: Date
  notes?: string
  photos?: string[]
}

// 📊 Service Metrics
export interface ServiceMetrics {
  estimatedDuration: number // minutes
  actualDuration?: number // minutes
  travelTime: number // minutes
  efficiency: number // percentage
  customerSatisfaction?: number // 1-5 rating
  qualityScore?: number // 1-100
  costEstimate: number
  actualCost?: number
  profitMargin: number
}

// 🔧 Comprehensive Service Order
export interface ServiceOrder {
  id: string
  orderNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  customerAddress: string
  
  // Service Details
  type: ServiceType
  status: ServiceOrderStatus
  priority: ServicePriority
  title: string
  description: string
  problemDescription?: string
  
  // Equipment Information
  equipmentId?: string
  equipmentName?: string
  equipmentModel?: string
  equipmentSerialNumber?: string
  
  // Scheduling
  requestedDate?: Date
  scheduledDate?: Date
  estimatedStartTime?: string
  estimatedEndTime?: string
  actualStartTime?: Date
  actualEndTime?: Date
  
  // Assignment
  assignedTechnicians: string[] // technician IDs
  requiredSkills: RequiredSkill[]
  
  // Service Details
  parts: ServicePart[]
  checklist: ServiceChecklistItem[]
  metrics: ServiceMetrics
  
  // Documentation
  photos: string[]
  documents: string[]
  notes: string
  internalNotes: string
  
  // AI Analysis
  aiAnalysis: {
    complexityScore: number // 1-100
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    estimatedDuration: number // minutes
    recommendedTechnician?: string
    suggestedParts: string[]
    similarCases: string[]
    confidence: number // 0-1
  }
  
  // Financial
  laborCost: number
  partsCost: number
  travelCost: number
  totalCost: number
  customerQuote?: number
  invoiceId?: string
  
  // Workflow
  approvalRequired: boolean
  approvedBy?: string
  approvedAt?: Date
  completionSignature?: string
  customerFeedback?: {
    rating: number // 1-5
    comments: string
    wouldRecommend: boolean
    submittedAt: Date
  }
  
  // Metadata
  createdBy: string
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  tags: string[]
}

// 📊 Service Analytics
export interface ServiceAnalytics {
  totalOrders: number
  completedOrders: number
  pendingOrders: number
  averageCompletionTime: number // hours
  averageCustomerRating: number
  totalRevenue: number
  averageOrderValue: number
  technicianUtilization: number // percentage
  onTimeCompletion: number // percentage
  firstTimeFixRate: number // percentage
  
  // Trends
  ordersByType: Record<ServiceType, number>
  ordersByPriority: Record<ServicePriority, number>
  ordersByStatus: Record<ServiceOrderStatus, number>
  monthlyRevenue: Array<{ month: string, revenue: number }>
  topTechnicians: Array<{ id: string, name: string, completedJobs: number, rating: number }>
  
  // AI Insights
  predictedDemand: Array<{ date: string, orders: number }>
  recommendedStaffing: Array<{ date: string, technicians: number }>
  equipmentFailurePredictions: Array<{ equipmentId: string, riskScore: number }>
}

// 🔍 Service Filters
export interface ServiceFilters {
  status?: ServiceOrderStatus[]
  type?: ServiceType[]
  priority?: ServicePriority[]
  assignedTechnician?: string[]
  customerId?: string
  equipmentId?: string
  dateFrom?: Date
  dateTo?: Date
  search?: string
  tags?: string[]
}

// 📱 Service Actions
export type ServiceAction = 
  | 'view_details'
  | 'edit_order'
  | 'assign_technician'
  | 'update_status'
  | 'add_parts'
  | 'complete_checklist'
  | 'upload_photos'
  | 'generate_invoice'
  | 'send_notification'
  | 'schedule_followup'
  | 'cancel_order'
  | 'duplicate_order'

// 📋 Service Summary (for cards/lists)
export interface ServiceOrderSummary {
  id: string
  orderNumber: string
  customerName: string
  type: ServiceType
  status: ServiceOrderStatus
  priority: ServicePriority
  title: string
  scheduledDate?: Date
  assignedTechnicians: string[]
  estimatedDuration: number
  totalCost: number
  aiComplexityScore: number
  lastUpdated: Date
}
